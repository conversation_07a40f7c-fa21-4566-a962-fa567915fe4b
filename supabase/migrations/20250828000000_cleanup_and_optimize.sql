-- Database Cleanup and Optimization Migration
-- This migration consolidates and optimizes the database schema

-- First, let's ensure all tables have proper structure without duplicates
-- We'll use IF NOT EXISTS to prevent errors on existing columns

-- Companies table optimization
DO $$ 
BEGIN
    -- Add missing columns only if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'companies' AND column_name = 'email') THEN
        ALTER TABLE public.companies ADD COLUMN email TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'companies' AND column_name = 'phone') THEN
        ALTER TABLE public.companies ADD COLUMN phone TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'companies' AND column_name = 'address') THEN
        ALTER TABLE public.companies ADD COLUMN address TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'companies' AND column_name = 'status') THEN
        ALTER TABLE public.companies ADD COLUMN status TEXT DEFAULT 'active';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'companies' AND column_name = 'subscription_plan') THEN
        ALTER TABLE public.companies ADD COLUMN subscription_plan TEXT DEFAULT 'basic';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'companies' AND column_name = 'max_users') THEN
        ALTER TABLE public.companies ADD COLUMN max_users INTEGER DEFAULT 10;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'companies' AND column_name = 'settings') THEN
        ALTER TABLE public.companies ADD COLUMN settings JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'companies' AND column_name = 'api_tokens') THEN
        ALTER TABLE public.companies ADD COLUMN api_tokens JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'companies' AND column_name = 'onboarded_at') THEN
        ALTER TABLE public.companies ADD COLUMN onboarded_at TIMESTAMP WITH TIME ZONE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'companies' AND column_name = 'onboarded_by') THEN
        ALTER TABLE public.companies ADD COLUMN onboarded_by UUID REFERENCES auth.users(id);
    END IF;
END $$;

-- Add constraints safely
DO $$
BEGIN
    -- Email format constraint
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'companies_email_format') THEN
        ALTER TABLE public.companies 
        ADD CONSTRAINT companies_email_format 
        CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');
    END IF;
    
    -- Status constraint
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'companies_status_check') THEN
        ALTER TABLE public.companies 
        ADD CONSTRAINT companies_status_check 
        CHECK (status IN ('active', 'suspended', 'inactive'));
    END IF;
    
    -- Subscription plan constraint
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'companies_subscription_plan_check') THEN
        ALTER TABLE public.companies 
        ADD CONSTRAINT companies_subscription_plan_check 
        CHECK (subscription_plan IN ('basic', 'pro', 'enterprise'));
    END IF;
END $$;

-- Optimize indexes for better performance
-- Drop existing indexes if they exist and recreate with better configurations
DROP INDEX IF EXISTS idx_companies_email;
DROP INDEX IF EXISTS idx_companies_status;
DROP INDEX IF EXISTS idx_user_roles_company_role;
DROP INDEX IF EXISTS idx_user_roles_user_id;

-- Create optimized indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_email_active 
ON public.companies(email) WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_status_created 
ON public.companies(status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_company_user 
ON public.user_roles(company_id, user_id, role);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_user_company 
ON public.user_roles(user_id, company_id) WHERE role != 'super_admin';

-- Optimize leads table indexes
DROP INDEX IF EXISTS idx_leads_company_created_status;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_company_status_created 
ON public.leads(company_id, status, created_at DESC) 
WHERE created_at > NOW() - INTERVAL '2 years';

-- Add partial index for active leads only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_active_company 
ON public.leads(company_id, created_at DESC) 
WHERE status NOT IN ('לקוח סגור', 'לא רלוונטי');

-- Optimize cases table indexes
DROP INDEX IF EXISTS idx_cases_company_created_status;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_company_status_created 
ON public.cases(company_id, status, created_at DESC) 
WHERE created_at > NOW() - INTERVAL '2 years';

-- Add partial index for open cases
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_open_company 
ON public.cases(company_id, created_at DESC) 
WHERE status NOT IN ('סגור', 'בוטל');

-- Optimize time entries indexes
DROP INDEX IF EXISTS idx_time_entries_company_created_rate;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_time_entries_company_case_date 
ON public.case_time_entries(company_id, case_id, created_at DESC) 
WHERE created_at > NOW() - INTERVAL '1 year';

-- Add covering index for dashboard queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_time_entries_dashboard_covering 
ON public.case_time_entries(company_id, created_at DESC) 
INCLUDE (duration, total_cost, case_id) 
WHERE created_at > NOW() - INTERVAL '1 year';

-- Create materialized view for dashboard performance
DROP MATERIALIZED VIEW IF EXISTS public.dashboard_metrics_cache;
CREATE MATERIALIZED VIEW public.dashboard_metrics_cache AS
SELECT 
    company_id,
    DATE_TRUNC('month', created_at) as month_year,
    'leads' as metric_type,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE status = 'לקוח סגור') as closed_count,
    COALESCE(SUM(value) FILTER (WHERE status = 'לקוח סגור'), 0) as total_value
FROM public.leads
WHERE created_at > NOW() - INTERVAL '2 years'
GROUP BY company_id, DATE_TRUNC('month', created_at)

UNION ALL

SELECT 
    company_id,
    DATE_TRUNC('month', created_at) as month_year,
    'cases' as metric_type,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE status = 'סגור') as closed_count,
    COALESCE(SUM(value), 0) as total_value
FROM public.cases
WHERE created_at > NOW() - INTERVAL '2 years'
GROUP BY company_id, DATE_TRUNC('month', created_at)

UNION ALL

SELECT 
    cte.company_id,
    DATE_TRUNC('month', cte.created_at) as month_year,
    'time_entries' as metric_type,
    COUNT(*) as total_count,
    0 as closed_count,
    COALESCE(SUM(cte.total_cost), 0) as total_value
FROM public.case_time_entries cte
WHERE cte.created_at > NOW() - INTERVAL '2 years'
GROUP BY cte.company_id, DATE_TRUNC('month', cte.created_at);

-- Index the materialized view
CREATE INDEX IF NOT EXISTS idx_dashboard_metrics_cache_company_month 
ON public.dashboard_metrics_cache(company_id, month_year DESC, metric_type);

-- Function to refresh the materialized view
CREATE OR REPLACE FUNCTION public.refresh_dashboard_cache()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.dashboard_metrics_cache;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT SELECT ON public.dashboard_metrics_cache TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_dashboard_cache() TO authenticated;

-- Add comments for documentation
COMMENT ON MATERIALIZED VIEW public.dashboard_metrics_cache IS 'Cached dashboard metrics for improved performance';
COMMENT ON FUNCTION public.refresh_dashboard_cache() IS 'Refreshes the dashboard metrics cache';

-- Analyze tables for better query planning
ANALYZE public.companies;
ANALYZE public.user_roles;
ANALYZE public.leads;
ANALYZE public.cases;
ANALYZE public.case_time_entries;
