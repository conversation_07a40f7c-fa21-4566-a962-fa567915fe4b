-- Create workflow_triggers table
-- This table stores trigger events that should initiate workflow executions

CREATE TABLE public.workflow_triggers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  entity_type VARCHAR(20) NOT NULL CHECK (entity_type IN ('lead', 'case')),
  entity_id UUID NOT NULL,
  old_status TEXT,
  new_status TEXT NOT NULL,
  company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  processed BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_workflow_triggers_processed ON public.workflow_triggers(processed) WHERE processed = false;
CREATE INDEX idx_workflow_triggers_entity ON public.workflow_triggers(entity_type, entity_id);
CREATE INDEX idx_workflow_triggers_company_id ON public.workflow_triggers(company_id);
CREATE INDEX idx_workflow_triggers_created_at ON public.workflow_triggers(created_at);

-- Enable RLS
ALTER TABLE public.workflow_triggers ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can view triggers from their company" ON public.workflow_triggers
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM public.user_roles 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "System can manage triggers" ON public.workflow_triggers
  FOR ALL USING (true); -- System needs full access for trigger creation and processing

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.workflow_triggers TO authenticated;

-- Function to create workflow trigger for case status changes
CREATE OR REPLACE FUNCTION public.create_case_workflow_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create trigger if status actually changed
  IF TG_OP = 'UPDATE' AND OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id
    ) VALUES (
      'case',
      NEW.id,
      OLD.status,
      NEW.status,
      NEW.company_id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create workflow trigger for lead status changes
CREATE OR REPLACE FUNCTION public.create_lead_workflow_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create trigger if status actually changed
  IF TG_OP = 'UPDATE' AND OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id
    ) VALUES (
      'lead',
      NEW.id,
      OLD.status,
      NEW.status,
      NEW.company_id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers on cases table
CREATE TRIGGER case_status_workflow_trigger
  AFTER UPDATE ON public.cases
  FOR EACH ROW
  EXECUTE FUNCTION public.create_case_workflow_trigger();

-- Create triggers on leads table
CREATE TRIGGER lead_status_workflow_trigger
  AFTER UPDATE ON public.leads
  FOR EACH ROW
  EXECUTE FUNCTION public.create_lead_workflow_trigger();

-- Add comments for documentation
COMMENT ON TABLE public.workflow_triggers IS 'Stores trigger events for workflow automation system';
COMMENT ON COLUMN public.workflow_triggers.entity_type IS 'Type of entity that triggered the workflow (lead or case)';
COMMENT ON COLUMN public.workflow_triggers.entity_id IS 'ID of the entity that triggered the workflow';
COMMENT ON COLUMN public.workflow_triggers.old_status IS 'Previous status before the change';
COMMENT ON COLUMN public.workflow_triggers.new_status IS 'New status after the change';
COMMENT ON COLUMN public.workflow_triggers.processed IS 'Whether this trigger has been processed by the workflow scheduler';
