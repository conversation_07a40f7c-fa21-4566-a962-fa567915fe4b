-- Add assigned_user_id field to cases table
-- This migration adds the ability to assign users to cases

-- Add assigned_user_id column to cases table
ALTER TABLE public.cases
ADD COLUMN IF NOT EXISTS assigned_user_id UUID REFERENCES auth.users(id);

-- Create index for better performance on assigned user queries
CREATE INDEX IF NOT EXISTS idx_cases_assigned_user_id ON public.cases(assigned_user_id);

-- Update the Case interface type to include assigned user information
-- This will be handled in the TypeScript code

-- Add comment to document the field
COMMENT ON COLUMN public.cases.assigned_user_id IS 'User assigned to handle this case';

-- Create task_reminders table to track reminder notifications
CREATE TABLE IF NOT EXISTS public.task_reminders (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  task_id UUID NOT NULL REFERENCES public.case_tasks(id) ON DELETE CASCADE,
  company_id UUID NOT NULL,
  reminder_type TEXT NOT NULL CHECK (reminder_type IN ('7_days', '3_days', '1_day')),
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'cancelled')),
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_task_reminders_task_id ON public.task_reminders(task_id);
CREATE INDEX IF NOT EXISTS idx_task_reminders_company_id ON public.task_reminders(company_id);
CREATE INDEX IF NOT EXISTS idx_task_reminders_scheduled_for ON public.task_reminders(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_task_reminders_status ON public.task_reminders(status);

-- Create composite index for efficient querying of pending reminders
CREATE INDEX IF NOT EXISTS idx_task_reminders_pending_scheduled ON public.task_reminders(status, scheduled_for)
WHERE status = 'pending';

-- Add comments to document the table
COMMENT ON TABLE public.task_reminders IS 'Tracks WhatsApp reminder notifications for task deadlines';
COMMENT ON COLUMN public.task_reminders.reminder_type IS 'Type of reminder: 7_days, 3_days, or 1_day before deadline';
COMMENT ON COLUMN public.task_reminders.scheduled_for IS 'When the reminder should be sent';
COMMENT ON COLUMN public.task_reminders.status IS 'Status of the reminder: pending, sent, failed, or cancelled';
