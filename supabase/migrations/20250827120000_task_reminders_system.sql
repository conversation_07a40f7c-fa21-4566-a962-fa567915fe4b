-- Task Reminders System Migration
-- Creates comprehensive task reminder system with WhatsApp notifications

-- Add reminders_enabled field to case_tasks table (default true for opt-in)
ALTER TABLE public.case_tasks
ADD COLUMN IF NOT EXISTS reminders_enabled BOOLEAN NOT NULL DEFAULT true;

-- Add comment to document the field
COMMENT ON COLUMN public.case_tasks.reminders_enabled IS 'Whether WhatsApp reminders are enabled for this task (default: true)';

-- Function to automatically create reminders when a task with deadline is created or updated
CREATE OR REPLACE FUNCTION create_task_reminders()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create reminders if task has a deadline, is not completed, and reminders are enabled
  IF NEW.deadline IS NOT NULL AND NEW.status != 'הושלם' AND NEW.reminders_enabled = true THEN
    -- Delete existing reminders for this task (in case of update)
    DELETE FROM public.task_reminders WHERE task_id = NEW.id;
    
    -- Create 7-day reminder (if deadline is more than 7 days away)
    IF NEW.deadline > NOW() + INTERVAL '7 days' THEN
      INSERT INTO public.task_reminders (
        task_id, 
        company_id, 
        reminder_type, 
        scheduled_for
      ) VALUES (
        NEW.id,
        NEW.company_id,
        '7_days',
        NEW.deadline - INTERVAL '7 days'
      );
    END IF;
    
    -- Create 3-day reminder (if deadline is more than 3 days away)
    IF NEW.deadline > NOW() + INTERVAL '3 days' THEN
      INSERT INTO public.task_reminders (
        task_id, 
        company_id, 
        reminder_type, 
        scheduled_for
      ) VALUES (
        NEW.id,
        NEW.company_id,
        '3_days',
        NEW.deadline - INTERVAL '3 days'
      );
    END IF;
    
    -- Create 1-day reminder (if deadline is more than 1 day away)
    IF NEW.deadline > NOW() + INTERVAL '1 day' THEN
      INSERT INTO public.task_reminders (
        task_id, 
        company_id, 
        reminder_type, 
        scheduled_for
      ) VALUES (
        NEW.id,
        NEW.company_id,
        '1_day',
        NEW.deadline - INTERVAL '1 day'
      );
    END IF;
  ELSE
    -- If task is completed, has no deadline, or reminders are disabled, cancel all pending reminders
    UPDATE public.task_reminders
    SET status = 'cancelled', updated_at = NOW()
    WHERE task_id = NEW.id AND status = 'pending';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically manage reminders
DROP TRIGGER IF EXISTS trigger_create_task_reminders ON public.case_tasks;
CREATE TRIGGER trigger_create_task_reminders
  AFTER INSERT OR UPDATE ON public.case_tasks
  FOR EACH ROW
  EXECUTE FUNCTION create_task_reminders();

-- Function to clean up reminders when task is deleted
CREATE OR REPLACE FUNCTION cleanup_task_reminders()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM public.task_reminders WHERE task_id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for cleanup
DROP TRIGGER IF EXISTS trigger_cleanup_task_reminders ON public.case_tasks;
CREATE TRIGGER trigger_cleanup_task_reminders
  BEFORE DELETE ON public.case_tasks
  FOR EACH ROW
  EXECUTE FUNCTION cleanup_task_reminders();

-- Create view for easy querying of pending reminders with task and case details
CREATE OR REPLACE VIEW pending_task_reminders AS
SELECT 
  tr.id as reminder_id,
  tr.task_id,
  tr.company_id,
  tr.reminder_type,
  tr.scheduled_for,
  tr.status,
  ct.title as task_title,
  ct.description as task_description,
  ct.deadline as task_deadline,
  ct.priority as task_priority,
  ct.assigned_to as task_assigned_to,
  c.id as case_id,
  c.title as case_title,
  c.assigned_user_id as case_assigned_user_id,
  comp.green_api_instance_id,
  comp.green_api_token
FROM public.task_reminders tr
JOIN public.case_tasks ct ON tr.task_id = ct.id
JOIN public.cases c ON ct.case_id = c.id
JOIN public.companies comp ON tr.company_id = comp.id
WHERE tr.status = 'pending' 
  AND tr.scheduled_for <= NOW()
  AND ct.status != 'הושלם'
  AND comp.green_api_instance_id IS NOT NULL 
  AND comp.green_api_token IS NOT NULL;

-- Grant necessary permissions
GRANT SELECT ON pending_task_reminders TO authenticated;
GRANT ALL ON public.task_reminders TO authenticated;

-- Add RLS policies for task_reminders table
ALTER TABLE public.task_reminders ENABLE ROW LEVEL SECURITY;

-- Policy for users to see reminders for their company's tasks
CREATE POLICY "Users can view task reminders for their company" ON public.task_reminders
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM public.user_roles 
      WHERE user_id = auth.uid()
    )
  );

-- Policy for users to manage reminders for their company's tasks
CREATE POLICY "Users can manage task reminders for their company" ON public.task_reminders
  FOR ALL USING (
    company_id IN (
      SELECT company_id FROM public.user_roles 
      WHERE user_id = auth.uid()
    )
  );
