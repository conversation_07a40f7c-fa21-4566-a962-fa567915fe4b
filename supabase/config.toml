project_id = "jihaizhvpddinhdysscd"

# Security Configuration for Edge Functions
# JWT verification should be enabled for authenticated operations

[functions.twilio-access-token]
verify_jwt = true  # Requires authentication for voice access

[functions.twilio-webhook]
verify_jwt = false  # External webhook from Twilio

[functions.voice-twiml]
verify_jwt = false  # External webhook from Twilio

[functions.green-api-webhook]
verify_jwt = false  # External webhook from Green API

[functions.green-api-send-message]
verify_jwt = true  # Requires authentication to send messages

[functions.create-user-with-password]
verify_jwt = true  # Admin operation - requires authentication

[functions.user-management]
verify_jwt = true  # Admin operation - requires authentication

[functions.workflow-management]
verify_jwt = true  # Admin operation - requires authentication

[functions.workflow-executor]
verify_jwt = false  # Internal system operation

[functions.workflow-scheduler]
verify_jwt = false  # Internal system operation
