import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface UpdateUserMetadataRequest {
  userId: string;
  updates: {
    full_name?: string;
    email?: string;
    phone?: string;
  };
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { userId, updates }: UpdateUserMetadataRequest = await req.json();

    if (!userId) {
      return new Response(
        JSON.stringify({ success: false, error: 'userId is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    if (!updates || Object.keys(updates).length === 0) {
      return new Response(
        JSON.stringify({ success: false, error: 'updates object is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Updating user metadata for:', userId, 'with updates:', JSON.stringify(updates, null, 2));

    // Get the current user from the request
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ success: false, error: 'Authorization header required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user: currentUser }, error: userError } = await supabaseAdmin.auth.getUser(token);

    if (userError || !currentUser) {
      console.error('Error getting current user:', userError);
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid authentication' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Current user:', currentUser.id, currentUser.email);

    // Check if user has permission to update users
    const { data: userRole, error: roleError } = await supabaseAdmin
      .from('user_roles')
      .select('role, company_id')
      .eq('user_id', currentUser.id)
      .single();

    if (roleError) {
      console.error('Error getting user role:', roleError);
      return new Response(
        JSON.stringify({ success: false, error: 'Failed to verify permissions' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Current user role:', userRole);

    // Only company_admin and super_admin can update users
    if (!userRole || (userRole.role !== 'company_admin' && userRole.role !== 'super_admin')) {
      return new Response(
        JSON.stringify({ success: false, error: 'Insufficient permissions' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Prepare the update object for auth.users
    const authUpdates: any = {};

    // Update email if provided
    if (updates.email) {
      authUpdates.email = updates.email;
    }

    // Update user metadata if full_name or phone is provided
    if (updates.full_name || updates.phone) {
      // First get current user metadata
      const { data: currentUser, error: getUserError } = await supabaseAdmin.auth.admin.getUserById(userId);

      if (getUserError) {
        throw new Error('Failed to get current user: ' + getUserError.message);
      }

      // Merge with existing metadata
      const existingMetadata = currentUser.user?.user_metadata || {};
      console.log('Existing metadata:', JSON.stringify(existingMetadata, null, 2));

      const metadataUpdates: any = {
        ...existingMetadata
      };

      if (updates.full_name !== undefined) {
        metadataUpdates.full_name = updates.full_name || '';
        console.log('Setting full_name to:', updates.full_name);
      }

      if (updates.phone !== undefined) {
        metadataUpdates.phone = updates.phone || '';
        console.log('Setting phone to:', updates.phone);
      }

      console.log('Final metadata updates:', JSON.stringify(metadataUpdates, null, 2));
      authUpdates.user_metadata = metadataUpdates;
    }

    // Update the user in auth
    console.log('About to update user with:', JSON.stringify(authUpdates, null, 2));
    const { data: updatedUser, error: updateError } = await supabaseAdmin.auth.admin.updateUserById(userId, authUpdates);

    if (updateError) {
      console.error('Error updating user:', updateError);
      throw new Error('Failed to update user: ' + updateError.message);
    }

    console.log('Successfully updated user metadata for:', userId);
    console.log('Updated user data:', JSON.stringify(updatedUser.user?.user_metadata, null, 2));

    return new Response(
      JSON.stringify({
        success: true,
        user: {
          id: updatedUser.user?.id,
          email: updatedUser.user?.email,
          full_name: updatedUser.user?.user_metadata?.full_name,
          phone: updatedUser.user?.user_metadata?.phone
        }
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error: any) {
    console.error('Error in update-user-metadata:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error'
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
};

serve(handler);
