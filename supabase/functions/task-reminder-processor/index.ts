import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface PendingReminder {
  reminder_id: string;
  task_id: string;
  company_id: string;
  reminder_type: '7_days' | '3_days' | '1_day';
  scheduled_for: string;
  task_title: string;
  task_description?: string;
  task_deadline: string;
  task_priority: string;
  task_assigned_to?: string;
  case_id: string;
  case_title: string;
  case_assigned_user_id?: string;
  green_api_instance_id: string;
  green_api_token: string;
}

interface UserProfile {
  id: string;
  full_name: string;
  phone: string;
  email: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log('Task reminder processor running...');

    // Get all pending reminders that are due
    const { data: pendingReminders, error: fetchError } = await supabaseAdmin
      .from('pending_task_reminders')
      .select('*');

    if (fetchError) {
      console.error('Error fetching pending reminders:', fetchError);
      throw fetchError;
    }

    console.log(`Found ${pendingReminders?.length || 0} pending reminders`);

    if (!pendingReminders || pendingReminders.length === 0) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'No pending reminders to process',
          processed: 0
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    let processedCount = 0;
    let errorCount = 0;

    // Process each reminder
    for (const reminder of pendingReminders as PendingReminder[]) {
      try {
        await processReminder(supabaseAdmin, reminder);
        processedCount++;
      } catch (error) {
        console.error(`Error processing reminder ${reminder.reminder_id}:`, error);
        errorCount++;
        
        // Mark reminder as failed
        await supabaseAdmin
          .from('task_reminders')
          .update({
            status: 'failed',
            error_message: error instanceof Error ? error.message : 'Unknown error',
            updated_at: new Date().toISOString()
          })
          .eq('id', reminder.reminder_id);
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Task reminder processing completed',
        processed: processedCount,
        errors: errorCount,
        total: pendingReminders.length
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error in task reminder processor:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

async function processReminder(supabaseAdmin: any, reminder: PendingReminder) {
  console.log(`Processing reminder ${reminder.reminder_id} for task ${reminder.task_title}`);

  // Determine who to send the reminder to
  const targetUserId = reminder.task_assigned_to || reminder.case_assigned_user_id;
  
  if (!targetUserId) {
    throw new Error('No assigned user found for task or case');
  }

  // Get user profile with phone number
  const { data: userProfile, error: userError } = await supabaseAdmin
    .from('user_profiles')
    .select('id, full_name, phone, email')
    .eq('id', targetUserId)
    .single();

  if (userError || !userProfile) {
    throw new Error(`User profile not found for user ${targetUserId}`);
  }

  const user = userProfile as UserProfile;

  if (!user.phone) {
    throw new Error(`No phone number found for user ${user.full_name}`);
  }

  // Create reminder message
  const message = createReminderMessage(reminder, user);

  // Send WhatsApp message using Green API
  await sendWhatsAppReminder(reminder, user.phone, message);

  // Mark reminder as sent
  await supabaseAdmin
    .from('task_reminders')
    .update({
      status: 'sent',
      sent_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('id', reminder.reminder_id);

  console.log(`Reminder sent successfully to ${user.full_name} (${user.phone})`);
}

function createReminderMessage(reminder: PendingReminder, user: UserProfile): string {
  const deadlineDate = new Date(reminder.task_deadline);
  const formattedDeadline = deadlineDate.toLocaleDateString('he-IL', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  const reminderTypeText = {
    '7_days': 'בעוד שבוע',
    '3_days': 'בעוד 3 ימים', 
    '1_day': 'מחר'
  }[reminder.reminder_type];

  const priorityEmoji = {
    'קריטי': '🔴',
    'גבוה': '🟠',
    'בינוני': '🟡',
    'נמוך': '🟢'
  }[reminder.task_priority] || '📋';

  return `${priorityEmoji} תזכורת משימה - ${reminderTypeText}

שלום ${user.full_name},

יש לך משימה שתסתיים ${reminderTypeText}:

📋 *משימה:* ${reminder.task_title}
📁 *תיק:* ${reminder.case_title}
⏰ *תאריך יעד:* ${formattedDeadline}
🎯 *עדיפות:* ${reminder.task_priority}

${reminder.task_description ? `📝 *פרטים:* ${reminder.task_description}` : ''}

אנא וודא שהמשימה תושלם בזמן.

בהצלחה! 💪`;
}

async function sendWhatsAppReminder(reminder: PendingReminder, phoneNumber: string, message: string) {
  // Format phone number (remove any non-digits and ensure international format)
  const formattedPhone = phoneNumber.replace(/\D/g, '');
  const chatId = formattedPhone.startsWith('972') ? formattedPhone : `972${formattedPhone.substring(1)}`;

  // Send message via GreenAPI - Extract first 4 digits for subdomain
  const instanceSubdomain = reminder.green_api_instance_id.substring(0, 4);
  const greenApiUrl = `https://${instanceSubdomain}.api.greenapi.com/waInstance${reminder.green_api_instance_id}/sendMessage/${reminder.green_api_token}`;
  
  const response = await fetch(greenApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      chatId: `${chatId}@c.us`,
      message: message,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Green API request failed: ${response.status} - ${errorText}`);
  }

  const result = await response.json();
  
  if (!result.idMessage) {
    throw new Error(`Green API did not return message ID: ${JSON.stringify(result)}`);
  }

  console.log(`WhatsApp message sent successfully. Message ID: ${result.idMessage}`);
  return result;
}
