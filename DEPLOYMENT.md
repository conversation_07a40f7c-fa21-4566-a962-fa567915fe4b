# 🚀 Production Deployment Guide

## 📋 Pre-Deployment Checklist

### 1. Code Preparation
- [ ] All features tested locally
- [ ] No console.log statements in production code
- [ ] All TypeScript errors resolved
- [ ] Build runs successfully (`npm run build`)

### 2. Environment Configuration
- [ ] `.env` file contains development settings only
- [ ] `.env.production.example` reviewed for production variables
- [ ] Production domain name decided

## 🔧 Vercel Deployment Steps

### Step 1: Deploy to Vercel
```bash
# If you haven't already, install Vercel CLI
npm i -g vercel

# Deploy (first time)
vercel

# Follow prompts:
# - Link to existing project? No
# - Project name: legal-hebrew-nexus
# - Directory: ./
# - Override settings? No
```

### Step 2: Set Environment Variables in Vercel Dashboard

1. **Go to Vercel Dashboard**: https://vercel.com/dashboard
2. **Select your project**: `legal-hebrew-nexus`
3. **Go to Settings > Environment Variables**
4. **Add these variables for Production environment:**

```
VITE_SUPABASE_URL = https://jihaizhvpddinhdysscd.supabase.co
VITE_SUPABASE_ANON_KEY = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppaGFpemh2cGRkaW5oZHlzc2NkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU4ODQwNjIsImV4cCI6MjA3MTQ2MDA2Mn0.EmbivKVP4hlM3WI5WpYOLrNvmmTACm9AnwrWRyMqc6I
VITE_ENVIRONMENT = production
VITE_APP_URL = https://your-project-name.vercel.app
VITE_ENABLE_ANALYTICS = true
VITE_ENABLE_SPEED_INSIGHTS = true
VITE_ENABLE_AUTO_REFRESH = true
```

### Step 3: Deploy to Production
```bash
# Deploy to production
vercel --prod
```

## 🔍 Post-Deployment Verification

### 1. Basic Functionality Test
- [ ] Application loads without errors
- [ ] User can log in/register
- [ ] Cases can be created and viewed
- [ ] Leads can be managed
- [ ] Time tracking works
- [ ] Navigation is smooth

### 2. Environment Verification
- [ ] Check browser console for environment: should show "production"
- [ ] Analytics should be enabled (check network tab)
- [ ] No development-only features visible
- [ ] Auto-refresh works for version updates

### 3. Performance Check
- [ ] Page load times < 3 seconds
- [ ] Images load properly
- [ ] Mobile responsiveness works
- [ ] Hebrew text displays correctly (RTL)

## 🔄 Future Deployments

### Automatic Deployments
Once connected to GitHub, Vercel will automatically deploy when you push to main branch.

### Manual Deployments
```bash
# Deploy latest changes
git push origin main

# Or manual deploy
vercel --prod
```

## 🚨 Rollback Procedure

If something goes wrong:

1. **Go to Vercel Dashboard**
2. **Select your project**
3. **Go to Deployments tab**
4. **Find the last working deployment**
5. **Click "..." > "Promote to Production"**

## 📊 Monitoring

### Built-in Monitoring
- **Vercel Analytics**: Automatically enabled with `VITE_ENABLE_ANALYTICS=true`
- **Speed Insights**: Automatically enabled with `VITE_ENABLE_SPEED_INSIGHTS=true`

### Manual Monitoring
- Check Vercel dashboard for deployment status
- Monitor Supabase dashboard for database performance
- Watch for error reports in browser console

## 🔐 Security Notes

- ✅ `.env` file is in `.gitignore` (never committed)
- ✅ Production variables are set in Vercel dashboard
- ✅ Supabase RLS policies are already configured
- ✅ HTTPS is automatically enabled by Vercel

## 🎯 Domain Setup (Optional)

To use a custom domain:

1. **Go to Vercel Dashboard > Settings > Domains**
2. **Add your domain**: `yourdomain.com`
3. **Update DNS records** as instructed by Vercel
4. **Update environment variable**: `VITE_APP_URL=https://yourdomain.com`

## 📞 Support

If you encounter issues:
1. Check Vercel deployment logs
2. Check browser console for errors
3. Verify environment variables are set correctly
4. Check Supabase dashboard for API issues

---

**Ready to deploy? Follow the steps above and your Legal Hebrew Nexus app will be live! 🎉**
