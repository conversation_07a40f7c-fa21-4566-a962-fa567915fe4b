/**
 * Service Worker for Background Sync
 * Handles offline data synchronization and caching for 100+ companies
 */

const CACHE_NAME = 'legal-app-v1756722059643';
const API_CACHE_NAME = 'legal-app-api-v1756722059643';

// Cache static assets
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/favicon.ico'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('📦 Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('📦 Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('✅ Service Worker installed');
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('🔄 Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
              console.log('🗑️ Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests
  if (url.hostname.includes('supabase.co')) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle static assets
  if (request.method === 'GET') {
    event.respondWith(handleStaticRequest(request));
    return;
  }
});

// Handle API requests with caching strategy
async function handleApiRequest(request) {
  const url = new URL(request.url);
  const isReadOperation = request.method === 'GET' || url.pathname.includes('/rpc/get_');
  
  try {
    // For read operations, try cache first
    if (isReadOperation) {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        console.log('📋 Serving from cache:', request.url);
        
        // Fetch fresh data in background
        fetch(request)
          .then(response => {
            if (response.ok) {
              const responseClone = response.clone();
              caches.open(API_CACHE_NAME)
                .then(cache => cache.put(request, responseClone));
            }
          })
          .catch(() => {}); // Ignore background fetch errors
        
        return cachedResponse;
      }
    }

    // Fetch from network
    const response = await fetch(request);
    
    // Cache successful read operations
    if (response.ok && isReadOperation) {
      const responseClone = response.clone();
      const cache = await caches.open(API_CACHE_NAME);
      
      // Only cache dashboard and list queries
      if (url.pathname.includes('dashboard') || 
          url.pathname.includes('leads') || 
          url.pathname.includes('cases') ||
          url.pathname.includes('get_dashboard_metrics')) {
        
        // Set cache headers
        const cachedResponse = new Response(responseClone.body, {
          status: responseClone.status,
          statusText: responseClone.statusText,
          headers: {
            ...Object.fromEntries(responseClone.headers.entries()),
            'sw-cache-timestamp': Date.now().toString(),
            'sw-cache-ttl': (5 * 60 * 1000).toString() // 5 minutes TTL
          }
        });
        
        cache.put(request, cachedResponse);
        console.log('💾 Cached API response:', request.url);
      }
    }

    return response;

  } catch (error) {
    console.error('🚨 Network request failed:', error);
    
    // For read operations, try to serve from cache
    if (isReadOperation) {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        console.log('📋 Serving stale cache due to network error:', request.url);
        return cachedResponse;
      }
    }

    // Return error response
    return new Response(
      JSON.stringify({ 
        error: 'Network error', 
        message: 'Unable to fetch data. Please check your connection.' 
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Handle static asset requests
async function handleStaticRequest(request) {
  try {
    // Try cache first
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Fetch from network and cache
    const response = await fetch(request);
    if (response.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, response.clone());
    }

    return response;

  } catch (error) {
    console.error('🚨 Static asset request failed:', error);
    
    // Try to serve from cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline page or error
    return new Response('Offline', { status: 503 });
  }
}

// Background sync event
self.addEventListener('sync', (event) => {
  console.log('🔄 Background sync triggered:', event.tag);
  
  if (event.tag === 'dashboard-sync') {
    event.waitUntil(syncDashboardData());
  } else if (event.tag === 'leads-sync') {
    event.waitUntil(syncLeadsData());
  } else if (event.tag === 'cases-sync') {
    event.waitUntil(syncCasesData());
  }
});

// Sync dashboard data in background
async function syncDashboardData() {
  try {
    console.log('📊 Syncing dashboard data...');
    
    // Get all companies from cache or storage
    const companies = await getStoredCompanies();
    
    for (const company of companies) {
      const url = `${self.location.origin}/api/dashboard/${company.id}`;
      const response = await fetch(url);
      
      if (response.ok) {
        const cache = await caches.open(API_CACHE_NAME);
        cache.put(url, response.clone());
        console.log(`✅ Synced dashboard for company ${company.id}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Dashboard sync failed:', error);
  }
}

// Sync leads data in background
async function syncLeadsData() {
  try {
    console.log('📞 Syncing leads data...');

    // Note: Disabled API sync as the endpoints don't exist in this application
    // The application uses Supabase directly for data access
    console.log('📋 Leads sync disabled - using Supabase direct access');

  } catch (error) {
    console.error('❌ Leads sync failed:', error);
  }
}

// Sync cases data in background
async function syncCasesData() {
  try {
    console.log('📁 Syncing cases data...');
    
    const companies = await getStoredCompanies();
    
    for (const company of companies) {
      const url = `${self.location.origin}/api/cases/${company.id}`;
      const response = await fetch(url);
      
      if (response.ok) {
        const cache = await caches.open(API_CACHE_NAME);
        cache.put(url, response.clone());
        console.log(`✅ Synced cases for company ${company.id}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Cases sync failed:', error);
  }
}

// Get stored companies from IndexedDB or localStorage
async function getStoredCompanies() {
  try {
    // Try to get from IndexedDB first
    const companies = await getFromIndexedDB('companies');
    if (companies) return companies;
    
    // Fallback to localStorage
    const stored = localStorage.getItem('companies');
    return stored ? JSON.parse(stored) : [];
    
  } catch (error) {
    console.error('Failed to get stored companies:', error);
    return [];
  }
}

// IndexedDB helper
function getFromIndexedDB(storeName) {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('LegalAppDB', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const getRequest = store.getAll();
      
      getRequest.onsuccess = () => resolve(getRequest.result);
      getRequest.onerror = () => reject(getRequest.error);
    };
  });
}

// Message event - handle messages from main thread
self.addEventListener('message', (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'CACHE_COMPANY_DATA':
      cacheCompanyData(data.companyId, data.data);
      break;
      
    case 'CLEAR_CACHE':
      clearAllCaches();
      break;
      
    case 'GET_CACHE_STATUS':
      getCacheStatus().then(status => {
        event.ports[0].postMessage(status);
      });
      break;
  }
});

// Cache company data
async function cacheCompanyData(companyId, data) {
  try {
    const cache = await caches.open(API_CACHE_NAME);
    const response = new Response(JSON.stringify(data), {
      headers: { 'Content-Type': 'application/json' }
    });
    
    await cache.put(`/company/${companyId}/data`, response);
    console.log(`💾 Cached data for company ${companyId}`);
    
  } catch (error) {
    console.error('Failed to cache company data:', error);
  }
}

// Clear all caches
async function clearAllCaches() {
  try {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map(name => caches.delete(name)));
    console.log('🗑️ All caches cleared');
    
  } catch (error) {
    console.error('Failed to clear caches:', error);
  }
}

// Get cache status
async function getCacheStatus() {
  try {
    const cache = await caches.open(API_CACHE_NAME);
    const keys = await cache.keys();
    
    return {
      cacheSize: keys.length,
      lastUpdated: Date.now(),
      cacheKeys: keys.map(req => req.url)
    };
    
  } catch (error) {
    console.error('Failed to get cache status:', error);
    return { cacheSize: 0, lastUpdated: null, cacheKeys: [] };
  }
}
