/**
 * Version checking utility for automatic app updates
 * Detects when a new version is available and prompts user to refresh
 */

interface VersionInfo {
  version: string;
  buildTimestamp: number;
  buildDate: string;
  environment: string;
}

interface VersionCheckOptions {
  checkInterval?: number; // milliseconds
  autoRefresh?: boolean;
  showNotification?: boolean;
  onUpdateAvailable?: (newVersion: VersionInfo, currentVersion: string | null) => void;
}

class VersionChecker {
  private checkInterval: number;
  public autoRefresh: boolean;
  public showNotification: boolean;
  private onUpdateAvailable?: (newVersion: VersionInfo, currentVersion: string | null) => void;
  private intervalId?: NodeJS.Timeout;
  private isChecking = false;

  constructor(options: VersionCheckOptions = {}) {
    this.checkInterval = options.checkInterval || 5 * 60 * 1000; // 5 minutes default
    this.autoRefresh = options.autoRefresh || false;
    this.showNotification = options.showNotification !== false; // default true
    this.onUpdateAvailable = options.onUpdateAvailable;
  }

  /**
   * Start periodic version checking
   */
  start(): void {
    console.log('🔍 Starting version checker...');
    
    // Initial check
    this.checkForUpdates();
    
    // Set up periodic checking
    this.intervalId = setInterval(() => {
      this.checkForUpdates();
    }, this.checkInterval);
  }

  /**
   * Stop version checking
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
      console.log('⏹️ Version checker stopped');
    }
  }

  /**
   * Check for updates manually
   */
  async checkForUpdates(): Promise<boolean> {
    if (this.isChecking) {
      return false;
    }

    this.isChecking = true;

    try {
      console.log('🔍 Checking for app updates...');
      
      // Fetch version info with cache busting
      const response = await fetch(`/version.json?t=${Date.now()}`, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        console.warn('⚠️ Failed to fetch version info:', response.status);
        return false;
      }

      const newVersionInfo: VersionInfo = await response.json();
      const currentVersion = this.getCurrentVersion();

      console.log('📋 Current version:', currentVersion);
      console.log('📋 Remote version:', newVersionInfo.version);

      if (currentVersion && currentVersion !== newVersionInfo.version) {
        console.log('🆕 New version available!');
        
        // Call custom handler if provided
        if (this.onUpdateAvailable) {
          this.onUpdateAvailable(newVersionInfo, currentVersion);
        } else {
          // Default behavior
          this.handleUpdateAvailable(newVersionInfo, currentVersion);
        }

        return true;
      } else {
        // Update stored version if it's the first time or same version
        this.setCurrentVersion(newVersionInfo.version);
        console.log('✅ App is up to date');
        return false;
      }

    } catch (error) {
      console.warn('⚠️ Version check failed:', error);
      return false;
    } finally {
      this.isChecking = false;
    }
  }

  /**
   * Handle when update is available
   */
  private handleUpdateAvailable(newVersionInfo: VersionInfo, currentVersion: string): void {
    if (this.autoRefresh) {
      console.log('🔄 Auto-refreshing to new version...');

      // Show a brief toast notification before refreshing
      if (typeof window !== 'undefined' && 'toast' in window) {
        // If toast is available, show brief notification
        try {
          (window as any).toast?.info?.('מעדכן לגרסה חדשה...', { duration: 2000 });
        } catch (e) {
          // Fallback if toast is not available
          console.log('📱 Updating to new version...');
        }
      }

      // Small delay to show the notification before refreshing
      setTimeout(() => {
        this.refreshApp(newVersionInfo.version);
      }, 1000); // 1 second delay
      return;
    }

    if (this.showNotification) {
      this.showUpdateNotification(newVersionInfo, currentVersion);
    }
  }

  /**
   * Show update notification to user
   */
  private showUpdateNotification(newVersionInfo: VersionInfo, currentVersion: string): void {
    const message = `גרסה חדשה זמינה!\n\nגרסה נוכחית: ${currentVersion}\nגרסה חדשה: ${newVersionInfo.version}\n\nהאם ברצונך לרענן את האפליקציה?`;
    
    if (confirm(message)) {
      this.refreshApp(newVersionInfo.version);
    }
  }

  /**
   * Refresh the app to load new version
   */
  private refreshApp(newVersion: string): void {
    console.log(`🔄 Refreshing app to version ${newVersion}...`);
    
    // Update stored version before refresh
    this.setCurrentVersion(newVersion);
    
    // Clear caches if possible
    this.clearCaches().finally(() => {
      // Force reload
      window.location.reload();
    });
  }

  /**
   * Clear browser caches
   */
  private async clearCaches(): Promise<void> {
    try {
      // Clear service worker caches
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => {
            console.log('🗑️ Clearing cache:', cacheName);
            return caches.delete(cacheName);
          })
        );
      }

      // Clear localStorage cache entries
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('sync_') || key.includes('cache')) {
          localStorage.removeItem(key);
        }
      });

      console.log('🧹 Caches cleared');
    } catch (error) {
      console.warn('⚠️ Failed to clear caches:', error);
    }
  }

  /**
   * Get current stored version
   */
  private getCurrentVersion(): string | null {
    return localStorage.getItem('app-version');
  }

  /**
   * Set current version in storage
   */
  private setCurrentVersion(version: string): void {
    localStorage.setItem('app-version', version);
    localStorage.setItem('app-version-timestamp', Date.now().toString());
  }

  /**
   * Force check for updates (public method)
   */
  async forceCheck(): Promise<boolean> {
    this.stop(); // Stop current checking
    const result = await this.checkForUpdates();
    this.start(); // Restart checking
    return result;
  }
}

// Export singleton instance
export const versionChecker = new VersionChecker({
  checkInterval: 5 * 60 * 1000, // Check every 5 minutes
  autoRefresh: true, // Auto-refresh without asking user
  showNotification: false // Don't show notification since we auto-refresh
});

// Export class for custom instances
export { VersionChecker };
export type { VersionInfo, VersionCheckOptions };
