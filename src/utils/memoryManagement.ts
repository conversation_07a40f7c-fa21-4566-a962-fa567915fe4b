/**
 * Memory Management Utilities
 * Helps prevent memory leaks and optimize performance
 */

import { useEffect, useRef, useCallback, useState } from 'react';

/**
 * Memory leak detector for development
 */
export class MemoryLeakDetector {
  private static instance: MemoryLeakDetector;
  private subscriptions = new Set<string>();
  private timers = new Set<NodeJS.Timeout>();
  private intervals = new Set<NodeJS.Timeout>();
  private eventListeners = new Map<string, { element: EventTarget; event: string; handler: EventListener }>();

  static getInstance(): MemoryLeakDetector {
    if (!MemoryLeakDetector.instance) {
      MemoryLeakDetector.instance = new MemoryLeakDetector();
    }
    return MemoryLeakDetector.instance;
  }

  trackSubscription(id: string): void {
    this.subscriptions.add(id);
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 Tracking subscription: ${id} (Total: ${this.subscriptions.size})`);
    }
  }

  untrackSubscription(id: string): void {
    this.subscriptions.delete(id);
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 Untracked subscription: ${id} (Remaining: ${this.subscriptions.size})`);
    }
  }

  trackTimer(timer: NodeJS.Timeout): void {
    this.timers.add(timer);
  }

  untrackTimer(timer: NodeJS.Timeout): void {
    this.timers.delete(timer);
  }

  trackInterval(interval: NodeJS.Timeout): void {
    this.intervals.add(interval);
  }

  untrackInterval(interval: NodeJS.Timeout): void {
    this.intervals.delete(interval);
  }

  trackEventListener(id: string, element: EventTarget, event: string, handler: EventListener): void {
    this.eventListeners.set(id, { element, event, handler });
  }

  untrackEventListener(id: string): void {
    this.eventListeners.delete(id);
  }

  getStats() {
    return {
      subscriptions: this.subscriptions.size,
      timers: this.timers.size,
      intervals: this.intervals.size,
      eventListeners: this.eventListeners.size,
      activeSubscriptions: Array.from(this.subscriptions),
    };
  }

  cleanup(): void {
    // Clear all tracked timers
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();

    // Clear all tracked intervals
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.clear();

    // Remove all tracked event listeners
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.eventListeners.clear();

    console.log('🧹 Memory leak detector cleanup completed');
  }
}

/**
 * Hook for safe timeouts that auto-cleanup
 */
export const useSafeTimeout = () => {
  const timeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  const setSafeTimeout = useCallback((callback: () => void, delay: number) => {
    const timeout = setTimeout(() => {
      callback();
      timeoutsRef.current.delete(timeout);
    }, delay);

    timeoutsRef.current.add(timeout);
    MemoryLeakDetector.getInstance().trackTimer(timeout);

    return timeout;
  }, []);

  const clearSafeTimeout = useCallback((timeout: NodeJS.Timeout) => {
    clearTimeout(timeout);
    timeoutsRef.current.delete(timeout);
    MemoryLeakDetector.getInstance().untrackTimer(timeout);
  }, []);

  useEffect(() => {
    return () => {
      // Cleanup all timeouts on unmount
      timeoutsRef.current.forEach(timeout => {
        clearTimeout(timeout);
        MemoryLeakDetector.getInstance().untrackTimer(timeout);
      });
      timeoutsRef.current.clear();
    };
  }, []);

  return { setSafeTimeout, clearSafeTimeout };
};

/**
 * Hook for safe intervals that auto-cleanup
 */
export const useSafeInterval = () => {
  const intervalsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  const setSafeInterval = useCallback((callback: () => void, delay: number) => {
    const interval = setInterval(callback, delay);
    intervalsRef.current.add(interval);
    MemoryLeakDetector.getInstance().trackInterval(interval);
    return interval;
  }, []);

  const clearSafeInterval = useCallback((interval: NodeJS.Timeout) => {
    clearInterval(interval);
    intervalsRef.current.delete(interval);
    MemoryLeakDetector.getInstance().untrackInterval(interval);
  }, []);

  useEffect(() => {
    return () => {
      // Cleanup all intervals on unmount
      intervalsRef.current.forEach(interval => {
        clearInterval(interval);
        MemoryLeakDetector.getInstance().untrackInterval(interval);
      });
      intervalsRef.current.clear();
    };
  }, []);

  return { setSafeInterval, clearSafeInterval };
};

/**
 * Hook for safe event listeners that auto-cleanup
 */
export const useSafeEventListener = () => {
  const listenersRef = useRef<Map<string, { element: EventTarget; event: string; handler: EventListener }>>(new Map());

  const addSafeEventListener = useCallback((
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ) => {
    const id = `${Date.now()}-${Math.random()}`;
    element.addEventListener(event, handler, options);
    
    const listenerInfo = { element, event, handler };
    listenersRef.current.set(id, listenerInfo);
    MemoryLeakDetector.getInstance().trackEventListener(id, element, event, handler);

    return () => {
      element.removeEventListener(event, handler);
      listenersRef.current.delete(id);
      MemoryLeakDetector.getInstance().untrackEventListener(id);
    };
  }, []);

  useEffect(() => {
    return () => {
      // Cleanup all event listeners on unmount
      listenersRef.current.forEach(({ element, event, handler }, id) => {
        element.removeEventListener(event, handler);
        MemoryLeakDetector.getInstance().untrackEventListener(id);
      });
      listenersRef.current.clear();
    };
  }, []);

  return { addSafeEventListener };
};

/**
 * Hook for stable callback references to prevent unnecessary re-renders
 */
export const useStableCallback = <T extends (...args: any[]) => any>(callback: T): T => {
  const callbackRef = useRef<T>(callback);
  
  // Update the ref with the latest callback
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // Return a stable function that calls the latest callback
  return useCallback(((...args: any[]) => {
    return callbackRef.current(...args);
  }) as T, []);
};

/**
 * Hook for debounced values to prevent excessive re-renders
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const { setSafeTimeout, clearSafeTimeout } = useSafeTimeout();
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (timeoutRef.current) {
      clearSafeTimeout(timeoutRef.current);
    }

    timeoutRef.current = setSafeTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      if (timeoutRef.current) {
        clearSafeTimeout(timeoutRef.current);
      }
    };
  }, [value, delay, setSafeTimeout, clearSafeTimeout]);

  return debouncedValue;
};

/**
 * Performance monitoring hook
 */
export const usePerformanceMonitor = (componentName: string) => {
  const renderCountRef = useRef(0);
  const lastRenderTime = useRef(Date.now());

  useEffect(() => {
    renderCountRef.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    lastRenderTime.current = now;

    if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 ${componentName} render #${renderCountRef.current} (${timeSinceLastRender}ms since last)`);
      
      // Warn about frequent re-renders
      if (timeSinceLastRender < 100 && renderCountRef.current > 5) {
        console.warn(`⚠️ ${componentName} is re-rendering frequently. Consider optimization.`);
      }
    }
  });

  return {
    renderCount: renderCountRef.current,
    getStats: () => ({
      renderCount: renderCountRef.current,
      componentName
    })
  };
};


