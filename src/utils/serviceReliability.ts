/**
 * Service Reliability Manager
 * Handles external service failures, retries, and fallbacks
 */

import { monitoring } from './monitoring';

export interface ServiceConfig {
  name: string;
  maxRetries: number;
  retryDelay: number;
  timeout: number;
  fallbackEnabled: boolean;
}

export interface RetryOptions {
  maxRetries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
  onRetry?: (attempt: number, error: Error) => void;
}

export class ServiceReliabilityManager {
  private static instance: ServiceReliabilityManager;
  private serviceStatus = new Map<string, { isHealthy: boolean; lastCheck: number; failures: number }>();
  private readonly DEFAULT_CONFIG: ServiceConfig = {
    name: 'default',
    maxRetries: 3,
    retryDelay: 1000,
    timeout: 10000,
    fallbackEnabled: true
  };

  static getInstance(): ServiceReliabilityManager {
    if (!ServiceReliabilityManager.instance) {
      ServiceReliabilityManager.instance = new ServiceReliabilityManager();
    }
    return ServiceReliabilityManager.instance;
  }

  /**
   * Execute a service call with retry logic and fallback
   */
  async executeWithRetry<T>(
    serviceName: string,
    operation: () => Promise<T>,
    options: RetryOptions = {},
    fallback?: () => Promise<T>
  ): Promise<T> {
    const config = { ...this.DEFAULT_CONFIG, ...options };
    let lastError: Error;

    for (let attempt = 1; attempt <= (config.maxRetries || 3); attempt++) {
      try {
        monitoring.logDebug(`Attempting ${serviceName} operation`, { attempt, maxRetries: config.maxRetries });
        
        const result = await this.withTimeout(operation(), config.timeout || 10000);
        
        // Mark service as healthy on success
        this.markServiceHealthy(serviceName);
        
        if (attempt > 1) {
          monitoring.logInfo(`${serviceName} operation succeeded after ${attempt} attempts`);
        }
        
        return result;
      } catch (error) {
        lastError = error as Error;
        
        monitoring.logWarn(`${serviceName} operation failed`, {
          attempt,
          error: lastError.message,
          maxRetries: config.maxRetries
        });

        // Mark service as unhealthy
        this.markServiceUnhealthy(serviceName);

        // Call retry callback if provided
        if (options.onRetry) {
          options.onRetry(attempt, lastError);
        }

        // Don't retry on the last attempt
        if (attempt === (config.maxRetries || 3)) {
          break;
        }

        // Calculate delay with optional exponential backoff
        const delay = options.exponentialBackoff 
          ? (config.retryDelay || 1000) * Math.pow(2, attempt - 1)
          : config.retryDelay || 1000;

        await this.sleep(delay);
      }
    }

    // All retries failed, try fallback if available
    if (fallback && config.fallbackEnabled) {
      monitoring.logWarn(`${serviceName} failed after all retries, attempting fallback`);
      try {
        return await fallback();
      } catch (fallbackError) {
        monitoring.logError(`${serviceName} fallback also failed`, { 
          originalError: lastError.message,
          fallbackError: (fallbackError as Error).message 
        });
        throw new Error(`${serviceName} and fallback both failed: ${lastError.message}`);
      }
    }

    // No fallback available or fallback disabled
    monitoring.logError(`${serviceName} failed after all retries with no fallback`, { 
      error: lastError.message,
      attempts: config.maxRetries 
    });
    throw lastError;
  }

  /**
   * Add timeout to any promise
   */
  private withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs);
      })
    ]);
  }

  /**
   * Sleep utility for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Mark a service as healthy
   */
  private markServiceHealthy(serviceName: string): void {
    this.serviceStatus.set(serviceName, {
      isHealthy: true,
      lastCheck: Date.now(),
      failures: 0
    });
  }

  /**
   * Mark a service as unhealthy
   */
  private markServiceUnhealthy(serviceName: string): void {
    const current = this.serviceStatus.get(serviceName) || { isHealthy: true, lastCheck: 0, failures: 0 };
    this.serviceStatus.set(serviceName, {
      isHealthy: false,
      lastCheck: Date.now(),
      failures: current.failures + 1
    });
  }

  /**
   * Check if a service is healthy
   */
  isServiceHealthy(serviceName: string): boolean {
    const status = this.serviceStatus.get(serviceName);
    return status ? status.isHealthy : true; // Assume healthy if no data
  }

  /**
   * Get service health statistics
   */
  getServiceStats(serviceName: string) {
    return this.serviceStatus.get(serviceName) || { isHealthy: true, lastCheck: 0, failures: 0 };
  }

  /**
   * Get all service health data
   */
  getAllServiceStats() {
    const stats: Record<string, any> = {};
    this.serviceStatus.forEach((status, serviceName) => {
      stats[serviceName] = status;
    });
    return stats;
  }

  /**
   * Reset service health status
   */
  resetServiceHealth(serviceName: string): void {
    this.serviceStatus.delete(serviceName);
  }

  /**
   * Circuit breaker pattern implementation
   */
  async executeWithCircuitBreaker<T>(
    serviceName: string,
    operation: () => Promise<T>,
    failureThreshold: number = 5,
    recoveryTimeout: number = 60000
  ): Promise<T> {
    const status = this.serviceStatus.get(serviceName);
    
    // If service has too many failures and hasn't recovered, fail fast
    if (status && !status.isHealthy && status.failures >= failureThreshold) {
      const timeSinceLastCheck = Date.now() - status.lastCheck;
      if (timeSinceLastCheck < recoveryTimeout) {
        throw new Error(`${serviceName} circuit breaker is open. Service unavailable.`);
      }
    }

    try {
      const result = await operation();
      this.markServiceHealthy(serviceName);
      return result;
    } catch (error) {
      this.markServiceUnhealthy(serviceName);
      throw error;
    }
  }
}

/**
 * Specific service wrappers with built-in reliability
 */
export class TwilioServiceWrapper {
  private reliability = ServiceReliabilityManager.getInstance();

  async makeCall(phoneNumber: string, companyId: string): Promise<any> {
    return this.reliability.executeWithRetry(
      'twilio-voice',
      async () => {
        // Original Twilio call logic here
        const response = await fetch('/api/twilio/call', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ phoneNumber, companyId })
        });
        
        if (!response.ok) {
          throw new Error(`Twilio call failed: ${response.statusText}`);
        }
        
        return response.json();
      },
      {
        maxRetries: 2,
        retryDelay: 2000,
        exponentialBackoff: true,
        onRetry: (attempt, error) => {
          monitoring.logWarn(`Twilio call retry attempt ${attempt}`, { error: error.message });
        }
      },
      // Fallback: Log the failed call for manual follow-up
      async () => {
        monitoring.logError('Twilio call failed, logging for manual follow-up', { phoneNumber, companyId });
        return { success: false, fallback: true, message: 'Call logged for manual follow-up' };
      }
    );
  }

  async getAccessToken(companyId: string): Promise<string> {
    return this.reliability.executeWithRetry(
      'twilio-access-token',
      async () => {
        // Original access token logic
        const response = await fetch('/api/twilio/access-token', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ companyId })
        });
        
        if (!response.ok) {
          throw new Error(`Access token failed: ${response.statusText}`);
        }
        
        const data = await response.json();
        return data.accessToken;
      },
      {
        maxRetries: 3,
        retryDelay: 1000,
        exponentialBackoff: true
      }
    );
  }
}

export class WhatsAppServiceWrapper {
  private reliability = ServiceReliabilityManager.getInstance();

  async sendMessage(phoneNumber: string, message: string, companyId: string): Promise<any> {
    return this.reliability.executeWithRetry(
      'whatsapp-send',
      async () => {
        // Original WhatsApp send logic
        const response = await fetch('/api/whatsapp/send', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ phoneNumber, message, companyId })
        });
        
        if (!response.ok) {
          throw new Error(`WhatsApp send failed: ${response.statusText}`);
        }
        
        return response.json();
      },
      {
        maxRetries: 3,
        retryDelay: 2000,
        exponentialBackoff: true,
        onRetry: (attempt, error) => {
          monitoring.logWarn(`WhatsApp send retry attempt ${attempt}`, { error: error.message });
        }
      },
      // Fallback: Queue message for later delivery
      async () => {
        monitoring.logWarn('WhatsApp send failed, queuing for later delivery', { phoneNumber, message, companyId });
        // TODO: Implement message queue
        return { success: false, queued: true, message: 'Message queued for later delivery' };
      }
    );
  }

  async syncMessages(companyId: string): Promise<any> {
    return this.reliability.executeWithCircuitBreaker(
      'whatsapp-sync',
      async () => {
        // Original sync logic
        const response = await fetch(`/api/whatsapp/sync/${companyId}`);
        
        if (!response.ok) {
          throw new Error(`WhatsApp sync failed: ${response.statusText}`);
        }
        
        return response.json();
      },
      5, // failure threshold
      300000 // 5 minute recovery timeout
    );
  }
}

// Export singleton instances
export const twilioService = new TwilioServiceWrapper();
export const whatsappService = new WhatsAppServiceWrapper();
export const serviceReliability = ServiceReliabilityManager.getInstance();
