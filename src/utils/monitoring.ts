/**
 * Application Monitoring and Logging System
 */

import { config } from '@/config/environment';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: Record<string, any>;
  userId?: string;
  companyId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
  stack?: string;
}

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count';
  timestamp: string;
  context?: Record<string, any>;
}

/**
 * Centralized logging and monitoring service
 */
export class MonitoringService {
  private static instance: MonitoringService;
  private logs: LogEntry[] = [];
  private metrics: PerformanceMetric[] = [];
  private sessionId: string;
  private maxLogEntries = 1000;
  private maxMetrics = 500;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.setupGlobalErrorHandlers();
    this.setupPerformanceMonitoring();
  }

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError('Unhandled Promise Rejection', {
        reason: event.reason,
        promise: event.promise
      });
    });

    // Handle global JavaScript errors
    window.addEventListener('error', (event) => {
      this.logError('Global JavaScript Error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });
    });

    // Handle resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.logError('Resource Loading Error', {
          element: event.target?.tagName,
          source: (event.target as any)?.src || (event.target as any)?.href
        });
      }
    }, true);
  }

  private setupPerformanceMonitoring(): void {
    // Monitor page load performance
    if (typeof window !== 'undefined' && 'performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          if (navigation) {
            this.recordMetric('page_load_time', navigation.loadEventEnd - navigation.fetchStart, 'ms');
            this.recordMetric('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart, 'ms');
            this.recordMetric('first_byte', navigation.responseStart - navigation.fetchStart, 'ms');
          }
        }, 0);
      });
    }
  }

  log(level: LogLevel, message: string, context?: Record<string, any>): void {
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Add to local storage
    this.logs.push(entry);
    
    // Trim logs if too many
    if (this.logs.length > this.maxLogEntries) {
      this.logs = this.logs.slice(-this.maxLogEntries);
    }

    // Console output in development
    if (config.features.debugging) {
      const consoleMethod = this.getConsoleMethod(level);
      consoleMethod(`[${LogLevel[level]}] ${message}`, context || '');
    }

    // Send to external monitoring service in production
    if (config.environment === 'production' && level >= LogLevel.ERROR) {
      this.sendToMonitoringService(entry);
    }
  }

  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case LogLevel.DEBUG:
        return console.debug;
      case LogLevel.INFO:
        return console.info;
      case LogLevel.WARN:
        return console.warn;
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        return console.error;
      default:
        return console.log;
    }
  }

  logDebug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  logInfo(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context);
  }

  logWarn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context);
  }

  logError(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context);
  }

  logCritical(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.CRITICAL, message, context);
  }

  recordMetric(name: string, value: number, unit: 'ms' | 'bytes' | 'count', context?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date().toISOString(),
      context
    };

    this.metrics.push(metric);

    // Trim metrics if too many
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    if (config.features.debugging) {
      console.log(`📊 Metric: ${name} = ${value}${unit}`, context || '');
    }
  }

  private async sendToMonitoringService(entry: LogEntry): Promise<void> {
    try {
      // TODO: Integrate with actual monitoring service (Sentry, LogRocket, etc.)
      // For now, we'll just store it locally
      const existingLogs = JSON.parse(localStorage.getItem('monitoring_logs') || '[]');
      existingLogs.push(entry);
      
      // Keep only last 100 entries in localStorage
      if (existingLogs.length > 100) {
        existingLogs.splice(0, existingLogs.length - 100);
      }
      
      localStorage.setItem('monitoring_logs', JSON.stringify(existingLogs));
    } catch (error) {
      console.error('Failed to send log to monitoring service:', error);
    }
  }

  getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level);
    }
    return [...this.logs];
  }

  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  exportDiagnostics(): string {
    const diagnostics = {
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      environment: config.environment,
      userAgent: navigator.userAgent,
      url: window.location.href,
      logs: this.getLogs(LogLevel.WARN), // Only warnings and above
      metrics: this.getMetrics(),
      performance: this.getPerformanceSnapshot()
    };

    return JSON.stringify(diagnostics, null, 2);
  }

  private getPerformanceSnapshot(): Record<string, any> {
    if (typeof window === 'undefined' || !('performance' in window)) {
      return {};
    }

    return {
      memory: (performance as any).memory ? {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
      } : null,
      timing: performance.timing ? {
        navigationStart: performance.timing.navigationStart,
        loadEventEnd: performance.timing.loadEventEnd,
        domContentLoadedEventEnd: performance.timing.domContentLoadedEventEnd
      } : null,
      navigation: performance.getEntriesByType ? 
        performance.getEntriesByType('navigation')[0] : null
    };
  }

  clearLogs(): void {
    this.logs = [];
  }

  clearMetrics(): void {
    this.metrics = [];
  }
}

/**
 * Performance timing decorator
 */
export function measurePerformance(name: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = performance.now();
      const monitoring = MonitoringService.getInstance();

      try {
        const result = await originalMethod.apply(this, args);
        const endTime = performance.now();
        monitoring.recordMetric(`${name}_duration`, endTime - startTime, 'ms');
        return result;
      } catch (error) {
        const endTime = performance.now();
        monitoring.recordMetric(`${name}_error_duration`, endTime - startTime, 'ms');
        monitoring.logError(`Performance measurement error in ${name}`, { error });
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * React hook for component performance monitoring
 */
export const useMonitoring = (componentName: string) => {
  const monitoring = MonitoringService.getInstance();

  const logComponentError = (error: Error, errorInfo?: any) => {
    monitoring.logError(`Component Error: ${componentName}`, {
      error: error.message,
      stack: error.stack,
      errorInfo
    });
  };

  const recordComponentMetric = (metricName: string, value: number, unit: 'ms' | 'bytes' | 'count') => {
    monitoring.recordMetric(`${componentName}_${metricName}`, value, unit);
  };

  return {
    logError: monitoring.logError.bind(monitoring),
    logWarn: monitoring.logWarn.bind(monitoring),
    logInfo: monitoring.logInfo.bind(monitoring),
    logDebug: monitoring.logDebug.bind(monitoring),
    recordMetric: recordComponentMetric,
    logComponentError
  };
};

// Global monitoring instance
export const monitoring = MonitoringService.getInstance();
