/* Emergency CSS to fix green background issue */

/* Force white backgrounds */
html, body, #root {
  background-color: white !important;
  color: #0f172a !important;
}

/* Force all background classes to white */
.bg-background,
.bg-card,
.bg-popover,
.bg-muted,
.bg-secondary {
  background-color: white !important;
}

/* Force all text colors to dark */
.text-foreground,
.text-card-foreground,
.text-popover-foreground,
.text-muted-foreground,
.text-secondary-foreground {
  color: #0f172a !important;
}

/* Primary colors */
.bg-primary {
  background-color: #003E66 !important;
}

.text-primary {
  color: #003E66 !important;
}

.text-primary-foreground {
  color: white !important;
}

/* Success colors */
.bg-success {
  background-color: #16a34a !important;
}

.text-success {
  color: #16a34a !important;
}

/* Warning colors */
.bg-warning {
  background-color: #f59e0b !important;
}

.text-warning {
  color: #f59e0b !important;
}

/* Destructive colors */
.bg-destructive {
  background-color: #dc2626 !important;
}

.text-destructive {
  color: #dc2626 !important;
}

/* Case values and hours - User requirements */
.case-value {
  color: #16a34a !important;
  font-weight: 500 !important;
}

.hours-logged {
  color: #2563eb !important;
  font-weight: 500 !important;
}

/* Borders */
.border {
  border-color: #e5e7eb !important;
}

.border-border {
  border-color: #e5e7eb !important;
}

/* Input styling */
.bg-input {
  background-color: #f9fafb !important;
}

/* Button styling */
.btn-primary {
  background: linear-gradient(to right, #003E66, #0056b3) !important;
  color: white !important;
}

/* Card styling */
.card-professional {
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

/* Override any green backgrounds that might appear */
[style*="background-color: rgb(0, 255, 0)"],
[style*="background-color: green"],
[style*="background: green"],
.bg-green-500,
.bg-green-400,
.bg-green-600 {
  background-color: white !important;
}
