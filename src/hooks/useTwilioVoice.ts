import { useState, useEffect, useCallback, useRef } from 'react';
import { Device, Call } from '@twilio/voice-sdk';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useSafeTimeout } from '@/utils/memoryManagement';

export interface VoiceCall {
  call: Call;
  leadId?: string;
  leadName?: string;
  phoneNumber?: string;
  startTime: Date;
  status: 'connecting' | 'connected' | 'disconnected' | 'failed';
  direction: 'outbound' | 'inbound';
}

export interface AudioDevices {
  speakers: MediaDeviceInfo[];
  microphones: MediaDeviceInfo[];
  selectedSpeaker?: string;
  selectedMicrophone?: string;
}

interface UseTwilioVoiceProps {
  autoInitialize?: boolean;
  companyId?: string;
}

export const useTwilioVoice = ({ autoInitialize = true, companyId }: UseTwilioVoiceProps = {}) => {
  const [device, setDevice] = useState<Device | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const [activeCall, setActiveCall] = useState<VoiceCall | null>(null);
  const [audioDevices, setAudioDevices] = useState<AudioDevices>({
    speakers: [],
    microphones: []
  });
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(1.0);

  const deviceRef = useRef<Device | null>(null);

  // Initialize Twilio Device
  const initializeDevice = useCallback(async () => {
    if (isInitializing || isInitialized) return;

    setIsInitializing(true);
    setInitializationError(null);

    try {
      // Get current user session
      const { data: { session } } = await supabase.auth.getSession();
      console.log('Current session:', session ? 'authenticated' : 'not authenticated');

      if (!session) {
        throw new Error('User not authenticated');
      }

      // Get access token from our Supabase function
      console.log('Calling twilio-access-token function...');
      const { data, error } = await supabase.functions.invoke('twilio-access-token', {
        body: {
          companyId: companyId
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (error) {
        console.error('Error from twilio-access-token function:', error);
        throw new Error(`Failed to get access token: ${error.message}`);
      }

      if (!data?.accessToken) {
        console.error('No access token in response:', data);
        throw new Error('No access token received');
      }

      console.log('Access token received, creating Twilio Device...');

      // Create Twilio Device
      const twilioDevice = new Device(data.accessToken, {
        logLevel: 'debug',
        codecPreferences: ['opus', 'pcmu'],
        fakeLocalDTMF: true,
        enableRingingState: true,
      });

      // Setup device event listeners
      twilioDevice.on('ready', () => {
        console.log('Twilio Device is ready');
        setIsInitialized(true);
        toast.success('Voice system initialized');
      });

      twilioDevice.on('error', (error) => {
        console.error('Twilio Device error:', error);
        setInitializationError(error.message);
        toast.error(`Voice system error: ${error.message}`);
      });

      twilioDevice.on('incoming', (call) => {
        console.log('Incoming call received:', call);
        handleIncomingCall(call);
      });

      twilioDevice.on('tokenWillExpire', async () => {
        console.log('Token will expire, refreshing...');
        await refreshToken();
      });

      // Register the device
      await twilioDevice.register();
      
      setDevice(twilioDevice);
      deviceRef.current = twilioDevice;

      // Get available audio devices
      await updateAudioDevices();

    } catch (error: any) {
      console.error('Failed to initialize Twilio Device:', error);
      setInitializationError(error.message);
      toast.error(`Failed to initialize voice system: ${error.message}`);
    } finally {
      setIsInitializing(false);
    }
  }, [isInitializing, isInitialized, companyId]);

  // Refresh access token
  const refreshToken = useCallback(async () => {
    if (!device) return;

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('No session');

      const { data, error } = await supabase.functions.invoke('twilio-access-token', {
        body: {
          companyId: companyId
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (error || !data?.accessToken) {
        throw new Error('Failed to refresh token');
      }

      device.updateToken(data.accessToken);
      console.log('Token refreshed successfully');
    } catch (error: any) {
      console.error('Failed to refresh token:', error);
      toast.error('Failed to refresh authentication');
    }
  }, [device, companyId]);

  // Handle incoming call
  const handleIncomingCall = useCallback((call: Call) => {
    const callInfo: VoiceCall = {
      call,
      startTime: new Date(),
      status: 'connecting',
      direction: 'inbound'
    };

    setActiveCall(callInfo);
    setupCallEventListeners(call, callInfo);

    toast.info('Incoming call', {
      duration: 10000,
      action: {
        label: 'Answer',
        onClick: () => acceptCall()
      }
    });
  }, []);

  // Setup call event listeners
  const setupCallEventListeners = useCallback((call: Call, callInfo: VoiceCall) => {
    call.on('accept', () => {
      console.log('Call accepted');
      setActiveCall(prev => prev ? { ...prev, status: 'connected' } : null);
    });

    call.on('disconnect', () => {
      console.log('Call disconnected');
      setActiveCall(null);
      setIsMuted(false);
    });

    call.on('cancel', () => {
      console.log('Call cancelled');
      setActiveCall(null);
    });

    call.on('reject', () => {
      console.log('Call rejected');
      setActiveCall(null);
    });

    call.on('error', (error) => {
      console.error('Call error:', error);
      setActiveCall(prev => prev ? { ...prev, status: 'failed' } : null);
      toast.error(`Call error: ${error.message}`);
    });
  }, []);

  // Make outbound call
  const makeCall = useCallback(async (phoneNumber: string, leadId?: string, leadName?: string) => {
    if (!device || !isInitialized) {
      toast.error('Voice system not initialized');
      return;
    }

    try {
      const params = {
        To: phoneNumber,
        ...(leadId && { leadId })
      };

      const call = await device.connect({ params });
      
      const callInfo: VoiceCall = {
        call,
        leadId,
        leadName,
        phoneNumber,
        startTime: new Date(),
        status: 'connecting',
        direction: 'outbound'
      };

      setActiveCall(callInfo);
      setupCallEventListeners(call, callInfo);

      toast.success(`Calling ${leadName || phoneNumber}...`);
      
    } catch (error: any) {
      console.error('Failed to make call:', error);
      toast.error(`Failed to make call: ${error.message}`);
    }
  }, [device, isInitialized, setupCallEventListeners]);

  // Hangup call
  const hangupCall = useCallback(() => {
    if (activeCall?.call) {
      activeCall.call.disconnect();
    }
  }, [activeCall]);

  // Accept incoming call
  const acceptCall = useCallback(() => {
    if (activeCall?.call && activeCall.direction === 'inbound') {
      activeCall.call.accept();
    }
  }, [activeCall]);

  // Reject incoming call
  const rejectCall = useCallback(() => {
    if (activeCall?.call && activeCall.direction === 'inbound') {
      activeCall.call.reject();
    }
  }, [activeCall]);

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (activeCall?.call) {
      if (isMuted) {
        activeCall.call.mute(false);
        setIsMuted(false);
      } else {
        activeCall.call.mute(true);
        setIsMuted(true);
      }
    }
  }, [activeCall, isMuted]);

  // Update audio devices
  const updateAudioDevices = useCallback(async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const speakers = devices.filter(device => device.kind === 'audiooutput');
      const microphones = devices.filter(device => device.kind === 'audioinput');
      
      setAudioDevices({ speakers, microphones });
    } catch (error) {
      console.error('Failed to enumerate audio devices:', error);
    }
  }, []);

  // Set speaker device
  const setSpeakerDevice = useCallback(async (deviceId: string) => {
    if (device && device.audio) {
      try {
        await device.audio.speakerDevices.set(deviceId);
        setAudioDevices(prev => ({ ...prev, selectedSpeaker: deviceId }));
      } catch (error) {
        console.error('Failed to set speaker device:', error);
      }
    }
  }, [device]);

  // Set microphone device
  const setMicrophoneDevice = useCallback(async (deviceId: string) => {
    if (device && device.audio) {
      try {
        await device.audio.setInputDevice(deviceId);
        setAudioDevices(prev => ({ ...prev, selectedMicrophone: deviceId }));
      } catch (error) {
        console.error('Failed to set microphone device:', error);
      }
    }
  }, [device]);

  // Set call volume
  const setCallVolume = useCallback((newVolume: number) => {
    if (device && device.audio) {
      device.audio.outgoing(newVolume);
      setVolume(newVolume);
    }
  }, [device]);

  // Auto-initialize if requested
  useEffect(() => {
    if (autoInitialize && !isInitialized && !isInitializing && !initializationError && companyId) {
      initializeDevice();
    }
  }, [autoInitialize, isInitialized, isInitializing, initializationError, initializeDevice, companyId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (deviceRef.current) {
        deviceRef.current.destroy();
      }
    };
  }, []);

  return {
    // State
    device,
    isInitialized,
    isInitializing,
    initializationError,
    activeCall,
    audioDevices,
    isMuted,
    volume,

    // Actions
    initializeDevice,
    makeCall,
    hangupCall,
    acceptCall,
    rejectCall,
    toggleMute,
    setSpeakerDevice,
    setMicrophoneDevice,
    setCallVolume,
    updateAudioDevices
  };
};
