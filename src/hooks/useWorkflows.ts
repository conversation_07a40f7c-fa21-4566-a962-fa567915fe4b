import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';
import { toast } from 'sonner';

export interface Workflow {
  id: string;
  company_id: string;
  name: string;
  description?: string;
  is_active: boolean;
  trigger_type: 'lead_status_change' | 'case_status_change';
  trigger_config: {
    from_status: string;
    to_status: string;
  };
  created_at: string;
  updated_at: string;
  created_by: string;
  workflow_steps: WorkflowStep[];
}

export interface WorkflowStep {
  id: string;
  workflow_id: string;
  step_order: number;
  step_type: 'send_whatsapp' | 'wait' | 'update_lead_status' | 'update_case_status' | 'create_case';
  step_config: any;
  created_at: string;
}

export interface CreateWorkflowData {
  name: string;
  description?: string;
  trigger_type: 'lead_status_change' | 'case_status_change';
  trigger_config: {
    from_status: string;
    to_status: string;
  };
  steps: Array<{
    step_type: 'send_whatsapp' | 'wait' | 'update_lead_status' | 'update_case_status' | 'create_case';
    step_config: any;
  }>;
}

export const useWorkflows = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentCompany } = useCompany();

  const fetchWorkflows = async () => {
    if (!currentCompany) {
      setWorkflows([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      const { data: { session } } = await supabase.auth.getSession();

      const { data, error } = await supabase.functions.invoke('workflow-management', {
        body: {
          action: 'list',
          companyId: currentCompany.id
        },
        headers: {
          Authorization: `Bearer ${session?.access_token}`,
        }
      });

      if (error) throw error;

      if (!data?.success) {
        throw new Error(data?.error || 'Failed to fetch workflows');
      }

      setWorkflows(data.workflows || []);
    } catch (error: any) {
      console.error('Error fetching workflows:', error);
      toast.error('שגיאה בטעינת זרימות העבודה');
      setWorkflows([]);
    } finally {
      setIsLoading(false);
    }
  };

  const createWorkflow = async (workflowData: CreateWorkflowData) => {
    if (!currentCompany) {
      throw new Error('No company selected');
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();

      const { data, error } = await supabase.functions.invoke('workflow-management', {
        body: {
          action: 'create',
          workflowData,
          companyId: currentCompany?.id
        },
        headers: {
          Authorization: `Bearer ${session?.access_token}`,
        }
      });

      if (error) throw error;

      if (!data?.success) {
        throw new Error(data?.error || 'Failed to create workflow');
      }

      toast.success('זרימת העבודה נוצרה בהצלחה');
      await fetchWorkflows(); // Refresh the list
      return data.workflow;
    } catch (error: any) {
      console.error('Error creating workflow:', error);
      toast.error('שגיאה ביצירת זרימת העבודה');
      throw error;
    }
  };

  const updateWorkflow = async (workflowId: string, workflowData: CreateWorkflowData) => {
    if (!currentCompany) {
      throw new Error('No company selected');
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();

      const { data, error } = await supabase.functions.invoke('workflow-management', {
        body: {
          action: 'update',
          workflowId,
          workflowData,
          companyId: currentCompany?.id
        },
        headers: {
          Authorization: `Bearer ${session?.access_token}`,
        }
      });

      if (error) throw error;

      if (!data?.success) {
        throw new Error(data?.error || 'Failed to update workflow');
      }

      toast.success('זרימת העבודה עודכנה בהצלחה');
      await fetchWorkflows(); // Refresh the list
      return data.workflow;
    } catch (error: any) {
      console.error('Error updating workflow:', error);
      toast.error('שגיאה בעדכון זרימת העבודה');
      throw error;
    }
  };

  const deleteWorkflow = async (workflowId: string) => {
    if (!currentCompany) {
      throw new Error('No company selected');
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();

      const { data, error } = await supabase.functions.invoke('workflow-management', {
        body: {
          action: 'delete',
          workflowId,
          companyId: currentCompany?.id
        },
        headers: {
          Authorization: `Bearer ${session?.access_token}`,
        }
      });

      if (error) throw error;

      if (!data?.success) {
        throw new Error(data?.error || 'Failed to delete workflow');
      }

      toast.success('זרימת העבודה נמחקה בהצלחה');
      await fetchWorkflows(); // Refresh the list
    } catch (error: any) {
      console.error('Error deleting workflow:', error);
      toast.error('שגיאה במחיקת זרימת העבודה');
      throw error;
    }
  };

  const toggleWorkflow = async (workflowId: string) => {
    if (!currentCompany) {
      throw new Error('No company selected');
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();

      const { data, error } = await supabase.functions.invoke('workflow-management', {
        body: {
          action: 'toggle',
          workflowId,
          companyId: currentCompany?.id
        },
        headers: {
          Authorization: `Bearer ${session?.access_token}`,
        }
      });

      if (error) throw error;

      if (!data?.success) {
        throw new Error(data?.error || 'Failed to toggle workflow');
      }

      const newStatus = data.workflow.is_active ? 'הופעלה' : 'הושבתה';
      toast.success(`זרימת העבודה ${newStatus} בהצלחה`);
      await fetchWorkflows(); // Refresh the list
    } catch (error: any) {
      console.error('Error toggling workflow:', error);
      toast.error('שגיאה בשינוי סטטוס זרימת העבודה');
      throw error;
    }
  };

  const getWorkflow = async (workflowId: string) => {
    if (!currentCompany) {
      throw new Error('No company selected');
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();

      const { data, error } = await supabase.functions.invoke('workflow-management', {
        body: {
          action: 'get',
          workflowId,
          companyId: currentCompany?.id
        },
        headers: {
          Authorization: `Bearer ${session?.access_token}`,
        }
      });

      if (error) throw error;

      if (!data?.success) {
        throw new Error(data?.error || 'Failed to get workflow');
      }

      return data.workflow;
    } catch (error: any) {
      console.error('Error getting workflow:', error);
      toast.error('שגיאה בטעינת זרימת העבודה');
      throw error;
    }
  };

  useEffect(() => {
    fetchWorkflows();
  }, [currentCompany?.id]);

  return {
    workflows,
    isLoading,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    toggleWorkflow,
    getWorkflow,
    refetch: fetchWorkflows
  };
};
