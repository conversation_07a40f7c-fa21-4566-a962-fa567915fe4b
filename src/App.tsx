import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AuthProvider } from "@/contexts/AuthContext";
import { CompanyProvider } from "@/contexts/CompanyContext";
import { ThemeProvider } from "@/components/theme/ThemeProvider";
import { SpeedInsights } from '@vercel/speed-insights/react';
import { Analytics } from '@vercel/analytics/react';
import { GlobalErrorBoundary } from '@/components/error/GlobalErrorBoundary';
import { monitoring } from '@/utils/monitoring';
import { config } from '@/config/environment';
import { isAnalyticsEnabled, isSpeedInsightsEnabled, isAutoRefreshEnabled, devLog } from '@/config/environment';

import Login from "./pages/Login";
import MainLayout from "./components/layout/MainLayout";
import AuthGuard from "./components/auth/AuthGuard";

import CaseDetail from "./pages/CaseDetail";
import NotFound from "./pages/NotFound";
import LeadsPage from "./pages/LeadsPage";
import WhatsAppPage from "./pages/WhatsAppPage";
import MarketingAutomationPage from "./pages/MarketingAutomationPage";
import CaseIntakePage from "./pages/CaseIntakePage";
import CaseOpenPage from "./pages/CaseOpenPage";
import CaseClosedPage from "./pages/CaseClosedPage";
import CasesPage from "./pages/CasesPage";


import DashboardPage from "./pages/DashboardPage";
import SuperAdminPage from "./pages/SuperAdminPage";
import CompanySettingsPage from "./pages/CompanySettingsPage";
import { VoiceTestPage } from "./pages/VoiceTestPage";
import BackgroundSyncService from "@/services/BackgroundSyncService";
import { versionChecker } from '@/utils/versionCheck';

// Optimized React Query configuration for performance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 60 * 1000, // 10 minutes - longer stale time for better performance
      gcTime: 15 * 60 * 1000, // 15 minutes - longer garbage collection time
      retry: 2, // Reduce retry attempts
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000), // Faster backoff
      refetchOnWindowFocus: false, // Don't refetch on window focus
      refetchOnMount: false, // Don't refetch on mount if data exists
      placeholderData: (previousData: any) => previousData, // Show old data while fetching new
    },
    mutations: {
      retry: 1, // Reduce mutation retries
      retryDelay: attemptIndex => Math.min(500 * 2 ** attemptIndex, 5000), // Faster mutation backoff
    },
  },
});

const ProtectedWrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthGuard>
    <CompanyProvider>
      <SidebarProvider>
        {children}
      </SidebarProvider>
    </CompanyProvider>
  </AuthGuard>
);

const App = () => {
  // Check if analytics should be enabled based on environment configuration
  const shouldEnableAnalytics = React.useMemo(() => {
    const analyticsEnabled = isAnalyticsEnabled();
    const speedInsightsEnabled = isSpeedInsightsEnabled();

    devLog('Analytics configuration:', {
      analytics: analyticsEnabled,
      speedInsights: speedInsightsEnabled,
      environment: import.meta.env.VITE_ENVIRONMENT
    });

    return { analytics: analyticsEnabled, speedInsights: speedInsightsEnabled };
  }, []);

  // Initialize background sync service for performance optimization
  React.useEffect(() => {
    const syncService = BackgroundSyncService.getInstance();
    syncService.start();

    return () => {
      syncService.stop();
    };
  }, []);

  // Initialize version checker for automatic updates
  React.useEffect(() => {
    // Configure version checker based on environment
    const autoRefreshEnabled = isAutoRefreshEnabled();

    devLog('Version checker configuration:', {
      autoRefresh: autoRefreshEnabled,
      checkInterval: 5 * 60 * 1000 // 5 minutes
    });

    // Update version checker configuration
    versionChecker.autoRefresh = autoRefreshEnabled;
    versionChecker.showNotification = !autoRefreshEnabled; // Show notification only if not auto-refreshing

    // Start version checking after a short delay to avoid blocking initial load
    const timer = setTimeout(() => {
      versionChecker.start();
    }, 3000); // 3 second delay

    return () => {
      clearTimeout(timer);
      versionChecker.stop();
    };
  }, []);

  // Initialize monitoring
  React.useEffect(() => {
    monitoring.logInfo('Application started', {
      environment: config.environment,
      version: '1.0.0',
      userAgent: navigator.userAgent
    });
  }, []);

  try {
    return (
      <GlobalErrorBoundary
        onError={(error, errorInfo) => {
          monitoring.logCritical('Global Error Boundary triggered', {
            error: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack
          });
        }}
      >
        <QueryClientProvider client={queryClient}>
          <ThemeProvider defaultTheme="light" storageKey="legal-nexus-ui-theme">
            <TooltipProvider>
              <Toaster />
              <Sonner />
              {shouldEnableAnalytics.speedInsights && <SpeedInsights />}
              {shouldEnableAnalytics.analytics && <Analytics />}
              <AuthProvider>
              <BrowserRouter>
                <div className="min-h-screen w-full hebrew-text bg-background loading-protection loaded">
                <Routes>
                  <Route path="/login" element={<Login />} />
                  <Route path="/" element={
                    <ProtectedWrapper>
                      <MainLayout />
                    </ProtectedWrapper>
                  }>
                    <Route index element={<DashboardPage />} />
                    <Route path="dashboard" element={<DashboardPage />} />
                    <Route path="office/leads" element={<LeadsPage />} />
                    <Route path="office/whatsapp" element={<WhatsAppPage />} />
                    <Route path="office/marketing-automation" element={<MarketingAutomationPage />} />

                    <Route path="cases" element={<CasesPage />} />
                    <Route path="cases/intake" element={<CaseIntakePage />} />
                    <Route path="cases/open" element={<CaseOpenPage />} />
                    <Route path="cases/closed" element={<CaseClosedPage />} />
                    <Route path="case/:id" element={<CaseDetail />} />

                    <Route path="super-admin" element={<SuperAdminPage />} />
                    <Route path="company-settings" element={<CompanySettingsPage />} />
                    <Route path="voice-test" element={<VoiceTestPage />} />
                  </Route>
                  <Route path="*" element={<NotFound />} />
                </Routes>
                </div>
              </BrowserRouter>
            </AuthProvider>
          </TooltipProvider>
        </ThemeProvider>
      </QueryClientProvider>
      </GlobalErrorBoundary>
    );
  } catch (error) {
    console.error('App rendering error:', error);
    return (
      <div style={{ padding: '20px', background: 'red', color: 'white' }}>
        <h1>Error in App</h1>
        <p>Check console for details</p>
      </div>
    );
  }
};

export default App;