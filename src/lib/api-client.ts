import { supabase } from '@/integrations/supabase/client';

// API client for external integrations (AI agent, webhooks, etc.)
export class ApiClient {
  private static instance: ApiClient;
  
  public static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  // Authenticate with service key for server-side operations
  private async authenticateService(serviceKey: string) {
    if (!serviceKey) {
      throw new Error('Service key is required');
    }

    // Get expected service key from environment
    const expectedKey = import.meta.env.VITE_API_SERVICE_KEY;
    if (!expectedKey) {
      throw new Error('API service key not configured');
    }

    // Use constant-time comparison to prevent timing attacks
    if (!this.constantTimeCompare(serviceKey, expectedKey)) {
      throw new Error('Invalid service key');
    }
  }

  // Constant-time string comparison to prevent timing attacks
  private constantTimeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }
    return result === 0;
  }

  // Lead Management API
  async createLead(data: {
    full_name: string;
    phone: string;
    email?: string;
    source?: string;
    status?: string;
    value?: number;
    notes?: string;
    company_id: string;
    user_id: string;
  }) {
    try {
      const { data: lead, error } = await supabase
        .from('leads')
        .insert(data)
        .select(`
          *,
          lead_activities(*)
        `)
        .single();

      if (error) throw error;
      return { success: true, data: lead };
    } catch (error) {
      console.error('Error creating lead:', error);
      return { success: false, error: error.message };
    }
  }

  async updateLeadStatus(leadId: string, status: string, notes?: string) {
    try {
      const { data: lead, error } = await supabase
        .from('leads')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', leadId)
        .select()
        .single();

      if (error) throw error;

      // Add activity log
      if (notes) {
        await supabase
          .from('lead_activities')
          .insert({
            lead_id: leadId,
            user_id: lead.user_id,
            company_id: lead.company_id,
            activity_type: 'status_change',
            description: `סטטוס שונה ל: ${status}. ${notes}`,
            metadata: { old_status: lead.status, new_status: status }
          });
      }

      return { success: true, data: lead };
    } catch (error) {
      console.error('Error updating lead status:', error);
      return { success: false, error: error.message };
    }
  }

  // Case Management API
  async createCase(data: {
    title: string;
    description?: string;
    case_type_id?: string;
    status?: string;
    value?: number;
    lead_id?: string;
    deadline?: string;
    company_id: string;
    user_id: string;
  }) {
    try {
      const { data: case_, error } = await supabase
        .from('cases')
        .insert({
          ...data,
          status: data.status || 'בקליטה'
        })
        .select(`
          *,
          case_type:case_types(id, name, hourly_rate),
          lead:leads(id, full_name, phone, email)
        `)
        .single();

      if (error) throw error;
      return { success: true, data: case_ };
    } catch (error) {
      console.error('Error creating case:', error);
      return { success: false, error: error.message };
    }
  }

  async updateCaseStatus(caseId: string, status: string, notes?: string) {
    try {
      // Get current case to track status change
      const { data: currentCase, error: fetchError } = await supabase
        .from('cases')
        .select('status, company_id')
        .eq('id', caseId)
        .single();

      if (fetchError) throw fetchError;

      const { data: case_, error } = await supabase
        .from('cases')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', caseId)
        .select(`
          *,
          case_type:case_types(id, name, hourly_rate),
          lead:leads(id, full_name, phone, email)
        `)
        .single();

      if (error) throw error;

      // Trigger workflow if status changed
      if (currentCase.status !== status) {
        try {
          await supabase.functions.invoke('workflow-executor', {
            body: {
              action: 'trigger',
              triggerData: {
                entity_type: 'case',
                entity_id: caseId,
                old_status: currentCase.status,
                new_status: status,
                company_id: currentCase.company_id
              }
            }
          });
        } catch (workflowError) {
          console.error('Error triggering case status workflow:', workflowError);
          // Don't fail the main operation if workflow fails
        }
      }

      return { success: true, data: case_ };
    } catch (error) {
      console.error('Error updating case status:', error);
      return { success: false, error: error.message };
    }
  }

  // Time Entry API
  async addTimeEntry(data: {
    case_id: string;
    task_id?: string;
    description?: string;
    duration: number; // in minutes
    start_time?: string;
    end_time?: string;
    company_id: string;
    user_id: string;
  }) {
    try {
      // No longer calculating total_cost here - it will be calculated based on case value
      const { data: timeEntry, error } = await supabase
        .from('case_time_entries')
        .insert(data)
        .select()
        .single();

      if (error) throw error;
      return { success: true, data: timeEntry };
    } catch (error) {
      console.error('Error adding time entry:', error);
      return { success: false, error: error.message };
    }
  }

  // WhatsApp Integration API
  async logWhatsAppActivity(data: {
    lead_id?: string;
    phone_number: string;
    message_type: 'incoming' | 'outgoing';
    message_content: string;
    company_id: string;
    user_id: string;
  }) {
    try {
      // Find or create lead by phone number
      let leadId = data.lead_id;
      
      if (!leadId) {
        const { data: existingLead } = await supabase
          .from('leads')
          .select('id')
          .eq('phone', data.phone_number)
          .eq('company_id', data.company_id)
          .single();

        if (existingLead) {
          leadId = existingLead.id;
        } else {
          // Create new lead
          const { data: newLead, error: leadError } = await supabase
            .from('leads')
            .insert({
              full_name: `WhatsApp ${data.phone_number}`,
              phone: data.phone_number,
              source: 'WhatsApp',
              status: 'ליד חדש',
              company_id: data.company_id,
              user_id: data.user_id
            })
            .select('id')
            .single();

          if (leadError) throw leadError;
          leadId = newLead.id;
        }
      }

      // Log activity
      const { data: activity, error } = await supabase
        .from('lead_activities')
        .insert({
          lead_id: leadId,
          user_id: data.user_id,
          company_id: data.company_id,
          activity_type: 'whatsapp',
          description: `WhatsApp ${data.message_type === 'incoming' ? 'נכנס' : 'יוצא'}: ${data.message_content}`,
          metadata: {
            message_type: data.message_type,
            phone_number: data.phone_number
          }
        })
        .select()
        .single();

      if (error) throw error;
      return { success: true, data: { activity, lead_id: leadId } };
    } catch (error) {
      console.error('Error logging WhatsApp activity:', error);
      return { success: false, error: error.message };
    }
  }

  // Search and Query API
  async searchLeads(query: string, company_id: string) {
    try {
      const { data: leads, error } = await supabase
        .from('leads')
        .select('*')
        .eq('company_id', company_id)
        .or(`full_name.ilike.%${query}%,phone.ilike.%${query}%,email.ilike.%${query}%`)
        .limit(10);

      if (error) throw error;
      return { success: true, data: leads };
    } catch (error) {
      console.error('Error searching leads:', error);
      return { success: false, error: error.message };
    }
  }

  async searchCases(query: string, company_id: string) {
    try {
      const { data: cases, error } = await supabase
        .from('cases')
        .select(`
          *,
          case_type:case_types(id, name),
          lead:leads(id, full_name, phone)
        `)
        .eq('company_id', company_id)
        .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
        .limit(10);

      if (error) throw error;
      return { success: true, data: cases };
    } catch (error) {
      console.error('Error searching cases:', error);
      return { success: false, error: error.message };
    }
  }

  // Case Types API
  async getCaseTypes(company_id: string) {
    try {
      const { data: caseTypes, error } = await supabase
        .from('case_types')
        .select('*')
        .eq('company_id', company_id)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return { success: true, data: caseTypes };
    } catch (error) {
      console.error('Error fetching case types:', error);
      return { success: false, error: error.message };
    }
  }
}
