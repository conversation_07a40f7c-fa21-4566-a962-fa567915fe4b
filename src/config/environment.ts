/**
 * Environment Configuration
 * Centralized configuration management for different environments
 */

interface EnvironmentConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  appUrl: string;
  environment: 'development' | 'staging' | 'production';
  features: {
    analytics: boolean;
    speedInsights: boolean;
    debugging: boolean;
    mockServices: boolean;
    versionChecker: boolean;
    autoRefresh: boolean;
  };
  analytics: {
    vercelAnalytics: boolean;
    vercelSpeedInsights: boolean;
    sentryDsn?: string;
    googleAnalyticsId?: string;
  };
}

const getEnvironmentConfig = (): EnvironmentConfig => {
  const env = import.meta.env.VITE_ENVIRONMENT || 'development';
  
  const configs: Record<string, EnvironmentConfig> = {
    development: {
      supabaseUrl: import.meta.env.VITE_SUPABASE_URL || 'http://localhost:54321',
      supabaseAnonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || 'local-key',
      appUrl: 'http://localhost:3000',
      environment: 'development',
      features: {
        analytics: false,
        speedInsights: false,
        debugging: true,
        mockServices: true,
        versionChecker: true,
        autoRefresh: true,
      },
      analytics: {
        vercelAnalytics: false,
        vercelSpeedInsights: false,
        sentryDsn: import.meta.env.VITE_SENTRY_DSN,
        googleAnalyticsId: import.meta.env.VITE_GA_TRACKING_ID,
      },
    },
    staging: {
      supabaseUrl: import.meta.env.VITE_SUPABASE_URL!,
      supabaseAnonKey: import.meta.env.VITE_SUPABASE_ANON_KEY!,
      appUrl: 'https://staging-legal-nexus.vercel.app',
      environment: 'staging',
      features: {
        analytics: true,
        speedInsights: true,
        debugging: true,
        mockServices: false,
        versionChecker: true,
        autoRefresh: true,
      },
      analytics: {
        vercelAnalytics: true,
        vercelSpeedInsights: true,
        sentryDsn: import.meta.env.VITE_SENTRY_DSN,
        googleAnalyticsId: import.meta.env.VITE_GA_TRACKING_ID,
      },
    },
    production: {
      supabaseUrl: import.meta.env.VITE_SUPABASE_URL!,
      supabaseAnonKey: import.meta.env.VITE_SUPABASE_ANON_KEY!,
      appUrl: 'https://legal-nexus.com',
      environment: 'production',
      features: {
        analytics: true,
        speedInsights: true,
        debugging: false,
        mockServices: false,
        versionChecker: true,
        autoRefresh: true,
      },
      analytics: {
        vercelAnalytics: true,
        vercelSpeedInsights: true,
        sentryDsn: import.meta.env.VITE_SENTRY_DSN,
        googleAnalyticsId: import.meta.env.VITE_GA_TRACKING_ID,
      },
    },
  };

  return configs[env] || configs.development;
};

export const config = getEnvironmentConfig();

// Helper functions for feature flags
export const isAnalyticsEnabled = (): boolean => {
  const enableAnalytics = import.meta.env.VITE_ENABLE_ANALYTICS !== 'false';
  return config.features.analytics && enableAnalytics;
};

export const isSpeedInsightsEnabled = (): boolean => {
  const enableSpeedInsights = import.meta.env.VITE_ENABLE_SPEED_INSIGHTS !== 'false';
  return config.features.speedInsights && enableSpeedInsights;
};

export const isAutoRefreshEnabled = (): boolean => {
  const enableAutoRefresh = import.meta.env.VITE_ENABLE_AUTO_REFRESH !== 'false';
  return config.features.autoRefresh && enableAutoRefresh;
};

export const isDevelopment = (): boolean => config.environment === 'development';
export const isProduction = (): boolean => config.environment === 'production';
export const isStaging = (): boolean => config.environment === 'staging';

// Console logging helper that respects environment
export const devLog = (message: string, ...args: any[]): void => {
  if (config.features.debugging) {
    console.log(`[${config.environment.toUpperCase()}] ${message}`, ...args);
  }
};

export default config;
