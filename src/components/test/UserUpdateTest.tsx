import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

/**
 * Test component to debug user phone number updates
 */
export const UserUpdateTest = () => {
  const [userId, setUserId] = useState('');
  const [phone, setPhone] = useState('');
  const [fullName, setFullName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testUpdate = async () => {
    if (!userId.trim()) {
      toast.error('Please enter a user ID');
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      console.log('Testing update for user:', userId);
      console.log('Updates:', { full_name: fullName, phone });

      const { data: updateResult, error: updateError } = await supabase.functions.invoke('update-user-metadata', {
        body: {
          userId: userId.trim(),
          updates: {
            full_name: fullName.trim() || undefined,
            phone: phone.trim() || undefined
          }
        }
      });

      console.log('Update result:', updateResult);
      console.log('Update error:', updateError);

      if (updateError) {
        throw updateError;
      }

      if (!updateResult?.success) {
        throw new Error(updateResult?.error || 'Failed to update user');
      }

      setResult(updateResult);
      toast.success('User updated successfully!');

    } catch (error: any) {
      console.error('Error updating user:', error);
      setResult({ error: error.message });
      toast.error('Error updating user: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testFetch = async () => {
    if (!userId.trim()) {
      toast.error('Please enter a user ID');
      return;
    }

    setIsLoading(true);

    try {
      // Get user directly from auth
      const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(userId.trim());
      
      if (authError) {
        throw authError;
      }

      console.log('Auth user data:', authUser);
      setResult({ authUser: authUser.user });
      toast.success('User fetched successfully!');

    } catch (error: any) {
      console.error('Error fetching user:', error);
      setResult({ error: error.message });
      toast.error('Error fetching user: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>User Update Debug Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="userId">User ID</Label>
            <Input
              id="userId"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              placeholder="Enter user UUID"
            />
          </div>

          <div>
            <Label htmlFor="fullName">Full Name</Label>
            <Input
              id="fullName"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              placeholder="Enter full name"
            />
          </div>

          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              placeholder="Enter phone number"
            />
          </div>

          <div className="flex gap-2">
            <Button onClick={testUpdate} disabled={isLoading}>
              {isLoading ? 'Updating...' : 'Test Update'}
            </Button>
            <Button onClick={testFetch} disabled={isLoading} variant="outline">
              {isLoading ? 'Fetching...' : 'Fetch User'}
            </Button>
          </div>

          {result && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Result:</h4>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
