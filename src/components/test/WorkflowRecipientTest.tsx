import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

/**
 * Test component to verify the WhatsApp workflow recipient selection functionality
 * This component simulates the StepConfigForm for WhatsApp actions
 */
export const WorkflowRecipientTest = () => {
  const [config, setConfig] = useState({
    recipient: 'lead',
    message: ''
  });

  const updateConfig = (key: string, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleTest = () => {
    console.log('Test Configuration:', config);
    alert(`Test Configuration:\nRecipient: ${config.recipient}\nMessage: ${config.message}`);
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>WhatsApp Workflow Recipient Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Recipient Selection */}
          <div>
            <Label>נמען ההודעה *</Label>
            <RadioGroup
              value={config.recipient}
              onValueChange={(value) => updateConfig('recipient', value)}
              className="mt-2"
            >
              <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="lead" id="recipient-lead" />
                <Label htmlFor="recipient-lead">שלח ללקוח</Label>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="assigned_user" id="recipient-user" />
                <Label htmlFor="recipient-user">שלח למשתמש המוקצה</Label>
              </div>
            </RadioGroup>
            {!config.recipient && (
              <p className="text-sm text-red-600 mt-1">שדה חובה</p>
            )}
          </div>
          
          {/* Message */}
          <div>
            <Label htmlFor="whatsapp-message">הודעה *</Label>
            <Textarea
              id="whatsapp-message"
              value={config.message}
              onChange={(e) => updateConfig('message', e.target.value)}
              placeholder="הכנס את ההודעה שתישלח"
              rows={4}
              className={!config.message?.trim() ? 'border-red-300 focus:border-red-500' : ''}
              required
            />
            {!config.message?.trim() && (
              <p className="text-sm text-red-600 mt-1">שדה חובה</p>
            )}
            
            {/* Available Variables */}
            <div className="mt-2">
              <p className="text-sm text-muted-foreground mb-2">משתנים זמינים:</p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="text-xs">{'{lead_name}'}</Badge>
                <Badge variant="outline" className="text-xs">{'{lead_phone}'}</Badge>
                <Badge variant="outline" className="text-xs">{'{lead_email}'}</Badge>
                <Badge variant="outline" className="text-xs">{'{lead_status}'}</Badge>
              </div>
            </div>
          </div>

          {/* Test Button */}
          <div className="pt-4">
            <Button onClick={handleTest} className="w-full">
              Test Configuration
            </Button>
          </div>

          {/* Current Configuration Display */}
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Current Configuration:</h4>
            <pre className="text-sm">
              {JSON.stringify(config, null, 2)}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
