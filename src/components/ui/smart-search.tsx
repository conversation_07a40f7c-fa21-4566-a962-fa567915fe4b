import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, User, FolderOpen, Calendar, MessageSquare } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useLeads } from '@/hooks/useLeads';
import { useCases } from '@/hooks/useCases';

interface SearchResult {
  id: string;
  title: string;
  subtitle: string;
  type: 'lead' | 'case' | 'action';
  icon: React.ComponentType<any>;
  action: () => void;
}

interface SmartSearchProps {
  placeholder?: string;
  className?: string;
}

export const SmartSearch: React.FC<SmartSearchProps> = ({ 
  placeholder = "חיפוש מהיר... (Ctrl+K)", 
  className = "" 
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const { leads } = useLeads();
  const { cases } = useCases();

  // Keyboard shortcut to focus search
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        inputRef.current?.focus();
        setIsOpen(true);
      }
      
      if (event.key === 'Escape') {
        setIsOpen(false);
        setQuery('');
        inputRef.current?.blur();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Search logic
  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    const searchResults: SearchResult[] = [];
    const lowerQuery = query.toLowerCase();

    // Search leads
    leads
      .filter(lead => 
        lead.full_name?.toLowerCase().includes(lowerQuery) ||
        lead.phone?.includes(query) ||
        lead.email?.toLowerCase().includes(lowerQuery)
      )
      .slice(0, 3)
      .forEach(lead => {
        searchResults.push({
          id: `lead-${lead.id}`,
          title: lead.full_name || 'ליד ללא שם',
          subtitle: `${lead.phone || ''} • ${lead.status}`,
          type: 'lead',
          icon: User,
          action: () => navigate('/office/leads')
        });
      });

    // Search cases
    cases
      .filter(case_ => 
        case_.title?.toLowerCase().includes(lowerQuery) ||
        case_.lead?.full_name?.toLowerCase().includes(lowerQuery)
      )
      .slice(0, 3)
      .forEach(case_ => {
        searchResults.push({
          id: `case-${case_.id}`,
          title: case_.title,
          subtitle: `${case_.lead?.full_name || ''} • ${case_.status}`,
          type: 'case',
          icon: FolderOpen,
          action: () => navigate(`/case/${case_.id}`)
        });
      });

    // Add quick actions if query matches
    if ('ליד חדש'.includes(lowerQuery) || 'new lead'.includes(lowerQuery)) {
      searchResults.push({
        id: 'action-new-lead',
        title: 'צור ליד חדש',
        subtitle: 'הוסף ליד חדש למערכת',
        type: 'action',
        icon: User,
        action: () => navigate('/office/leads')
      });
    }

    if ('תיק חדש'.includes(lowerQuery) || 'new case'.includes(lowerQuery)) {
      searchResults.push({
        id: 'action-new-case',
        title: 'צור תיק חדש',
        subtitle: 'הוסף תיק חדש למערכת',
        type: 'action',
        icon: FolderOpen,
        action: () => navigate('/cases')
      });
    }

    if ('וואטסאפ'.includes(lowerQuery) || 'whatsapp'.includes(lowerQuery)) {
      searchResults.push({
        id: 'action-whatsapp',
        title: 'וואטסאפ',
        subtitle: 'עבור לעמוד וואטסאפ',
        type: 'action',
        icon: MessageSquare,
        action: () => navigate('/office/whatsapp')
      });
    }

    setResults(searchResults.slice(0, 6));
    setSelectedIndex(0);
  }, [query, leads, cases, navigate]);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
        break;
      case 'Enter':
        event.preventDefault();
        if (results[selectedIndex]) {
          results[selectedIndex].action();
          setIsOpen(false);
          setQuery('');
          inputRef.current?.blur();
        }
        break;
    }
  };

  const handleResultClick = (result: SearchResult) => {
    result.action();
    setIsOpen(false);
    setQuery('');
    inputRef.current?.blur();
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          ref={inputRef}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onBlur={() => setTimeout(() => setIsOpen(false), 200)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="pr-10"
        />
      </div>

      {isOpen && results.length > 0 && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-50 shadow-lg">
          <CardContent className="p-0">
            {results.map((result, index) => {
              const Icon = result.icon;
              return (
                <div
                  key={result.id}
                  className={`flex items-center gap-3 p-3 cursor-pointer border-b last:border-b-0 transition-colors duration-200 ${
                    index === selectedIndex ? 'bg-muted/20' : 'hover:bg-muted/10'
                  }`}
                  onClick={() => handleResultClick(result)}
                >
                  <Icon className="w-4 h-4 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">{result.title}</div>
                    <div className="text-xs text-muted-foreground truncate">{result.subtitle}</div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {result.type === 'lead' && 'ליד'}
                    {result.type === 'case' && 'תיק'}
                    {result.type === 'action' && 'פעולה'}
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
