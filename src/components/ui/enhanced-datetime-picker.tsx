import React, { useState, useEffect } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CalendarIcon, Clock } from "lucide-react";
import { format, parse, isValid } from "date-fns";
import { he } from "date-fns/locale";
import { cn } from "@/lib/utils";

interface EnhancedDateTimePickerProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  label?: string;
  required?: boolean;
}

export const EnhancedDateTimePicker: React.FC<EnhancedDateTimePickerProps> = ({
  value,
  onChange,
  placeholder = "בחר תאריך ושעה",
  disabled = false,
  className,
  label,
  required = false,
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [selectedTime, setSelectedTime] = useState<string>("");
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  // Parse initial value
  useEffect(() => {
    if (value) {
      const date = new Date(value);
      if (isValid(date)) {
        setSelectedDate(date);
        setSelectedTime(format(date, "HH:mm"));
      }
    } else {
      setSelectedDate(undefined);
      setSelectedTime("");
    }
  }, [value]);

  // Generate time options (every 15 minutes)
  const timeOptions = React.useMemo(() => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        options.push(timeString);
      }
    }
    return options;
  }, []);

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    if (date && selectedTime) {
      updateDateTime(date, selectedTime);
    }
    setIsCalendarOpen(false);
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    if (selectedDate && time) {
      updateDateTime(selectedDate, time);
    }
  };

  const updateDateTime = (date: Date, time: string) => {
    try {
      const [hours, minutes] = time.split(':').map(Number);
      const newDateTime = new Date(date);
      newDateTime.setHours(hours, minutes, 0, 0);
      onChange(newDateTime.toISOString());
    } catch (error) {
      console.error('Error updating date time:', error);
    }
  };

  const handleClear = () => {
    setSelectedDate(undefined);
    setSelectedTime("");
    onChange("");
  };

  const handleToday = () => {
    const today = new Date();
    setSelectedDate(today);
    const currentTime = format(today, "HH:mm");
    setSelectedTime(currentTime);
    updateDateTime(today, currentTime);
  };

  const handleTomorrow = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    setSelectedDate(tomorrow);
    setSelectedTime("09:00");
    updateDateTime(tomorrow, "09:00");
  };

  const formatDisplayValue = () => {
    if (!selectedDate) return "";
    
    const dateStr = format(selectedDate, "dd/MM/yyyy", { locale: he });
    const timeStr = selectedTime || "00:00";
    return `${dateStr} ${timeStr}`;
  };

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </Label>
      )}
      
      <div className="flex gap-2">
        {/* Date Picker */}
        <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "flex-1 justify-start text-left font-normal",
                !selectedDate && "text-muted-foreground"
              )}
              disabled={disabled}
            >
              <CalendarIcon className="ml-2 h-4 w-4" />
              {selectedDate ? (
                format(selectedDate, "dd/MM/yyyy", { locale: he })
              ) : (
                "בחר תאריך"
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="p-3 border-b">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleToday}
                  className="flex-1"
                >
                  היום
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTomorrow}
                  className="flex-1"
                >
                  מחר
                </Button>
              </div>
            </div>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={handleDateSelect}
              disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
              initialFocus
              locale={he}
            />
          </PopoverContent>
        </Popover>

        {/* Time Picker */}
        <Select value={selectedTime} onValueChange={handleTimeSelect} disabled={disabled}>
          <SelectTrigger className="w-32">
            <Clock className="ml-2 h-4 w-4" />
            <SelectValue placeholder="שעה" />
          </SelectTrigger>
          <SelectContent className="max-h-60">
            {timeOptions.map((time) => (
              <SelectItem key={time} value={time}>
                {time}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Clear Button */}
        {(selectedDate || selectedTime) && (
          <Button
            variant="outline"
            size="icon"
            onClick={handleClear}
            disabled={disabled}
            className="shrink-0"
          >
            ✕
          </Button>
        )}
      </div>

      {/* Display current selection */}
      {formatDisplayValue() && (
        <div className="text-sm text-muted-foreground bg-muted/30 p-2 rounded border">
          <strong>נבחר:</strong> {formatDisplayValue()}
        </div>
      )}

      {/* Quick time presets */}
      {selectedDate && (
        <div className="flex gap-1 flex-wrap">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleTimeSelect("09:00")}
            className="text-xs h-6 px-2"
          >
            09:00
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleTimeSelect("12:00")}
            className="text-xs h-6 px-2"
          >
            12:00
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleTimeSelect("14:00")}
            className="text-xs h-6 px-2"
          >
            14:00
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleTimeSelect("17:00")}
            className="text-xs h-6 px-2"
          >
            17:00
          </Button>
        </div>
      )}
    </div>
  );
};
