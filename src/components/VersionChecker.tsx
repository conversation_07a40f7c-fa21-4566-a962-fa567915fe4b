/**
 * Version Checker Component
 * Shows current app version and allows manual update checks
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Info } from 'lucide-react';
import { versionChecker } from '@/utils/versionCheck';
import { toast } from 'sonner';

interface VersionInfo {
  version: string;
  buildTimestamp: number;
  buildDate: string;
  environment: string;
}

export const VersionChecker: React.FC = () => {
  const [currentVersion, setCurrentVersion] = React.useState<string | null>(null);
  const [isChecking, setIsChecking] = React.useState(false);
  const [lastCheck, setLastCheck] = React.useState<Date | null>(null);

  React.useEffect(() => {
    // Get current version from localStorage
    const version = localStorage.getItem('app-version');
    setCurrentVersion(version);
  }, []);

  const handleManualCheck = async () => {
    setIsChecking(true);
    try {
      const hasUpdate = await versionChecker.forceCheck();
      setLastCheck(new Date());
      
      if (hasUpdate) {
        toast.success('עדכון זמין! האפליקציה תתרענן בקרוב.');
      } else {
        toast.info('האפליקציה מעודכנת לגרסה האחרונה.');
      }
    } catch (error) {
      console.error('Version check failed:', error);
      toast.error('בדיקת עדכונים נכשלה. נסה שוב מאוחר יותר.');
    } finally {
      setIsChecking(false);
    }
  };

  const formatVersion = (version: string | null) => {
    if (!version) return 'לא ידוע';
    
    // Extract timestamp from version (format: v1234567890)
    const timestamp = version.replace('v', '');
    if (timestamp && !isNaN(Number(timestamp))) {
      const date = new Date(Number(timestamp));
      return `${version} (${date.toLocaleDateString('he-IL')} ${date.toLocaleTimeString('he-IL')})`;
    }
    
    return version;
  };

  return (
    <div className="flex items-center gap-2 text-sm text-muted-foreground">
      <Info className="h-4 w-4" />
      <span>גרסה:</span>
      <Badge variant="outline" className="font-mono text-xs">
        {formatVersion(currentVersion)}
      </Badge>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={handleManualCheck}
        disabled={isChecking}
        className="h-6 px-2"
      >
        <RefreshCw className={`h-3 w-3 ${isChecking ? 'animate-spin' : ''}`} />
        <span className="mr-1">בדוק עדכונים</span>
      </Button>
      
      {lastCheck && (
        <span className="text-xs text-muted-foreground">
          נבדק לאחרונה: {lastCheck.toLocaleTimeString('he-IL')}
        </span>
      )}
    </div>
  );
};

export default VersionChecker;
