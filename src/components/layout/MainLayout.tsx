import { Outlet } from "react-router-dom";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { Header } from "@/components/layout/Header";
import { useKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts";
import { VersionChecker } from "@/components/VersionChecker";

const MainLayout = () => {
  useKeyboardShortcuts();

  return (
    <>
      <AppSidebar />
      <div className="flex flex-col min-h-screen w-full">
        <Header />
        <main className="flex-1 p-6 bg-background">
          <Outlet />
        </main>
        <footer className="border-t border-border bg-background/50 backdrop-blur-sm p-4">
          <div className="flex justify-center">
            <VersionChecker />
          </div>
        </footer>
      </div>
    </>
  );
};

export default MainLayout;