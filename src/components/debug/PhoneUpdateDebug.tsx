import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useUserManagement } from '@/hooks/useUserManagement';
import { toast } from 'sonner';

/**
 * Debug component to test phone number updates
 * Add this to any page to test the functionality
 */
export const PhoneUpdateDebug = () => {
  const { users, fetchUsers, updateUser } = useUserManagement();
  const [selectedUserId, setSelectedUserId] = useState('');
  const [newPhone, setNewPhone] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const selectedUser = users.find(u => u.id === selectedUserId);

  const handleUpdate = async () => {
    if (!selectedUserId || !newPhone.trim()) {
      toast.error('Please select a user and enter a phone number');
      return;
    }

    setIsLoading(true);
    try {
      console.log('=== PHONE UPDATE DEBUG ===');
      console.log('Selected user:', selectedUser);
      console.log('Current phone:', selectedUser?.phone);
      console.log('New phone:', newPhone);
      
      const success = await updateUser(selectedUserId, { phone: newPhone.trim() });
      
      if (success) {
        toast.success('Phone updated successfully!');
        // Refresh users to see the change
        setTimeout(() => {
          fetchUsers();
        }, 1000);
      } else {
        toast.error('Failed to update phone');
      }
    } catch (error) {
      console.error('Error in phone update:', error);
      toast.error('Error updating phone');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await fetchUsers();
      toast.success('Users refreshed');
    } catch (error) {
      toast.error('Failed to refresh users');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Phone Update Debug Tool</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="user-select">Select User</Label>
            <select
              id="user-select"
              value={selectedUserId}
              onChange={(e) => {
                setSelectedUserId(e.target.value);
                const user = users.find(u => u.id === e.target.value);
                setNewPhone(user?.phone || '');
              }}
              className="w-full p-2 border rounded"
            >
              <option value="">Select a user...</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.full_name || user.email} - Current: {user.phone || 'No phone'}
                </option>
              ))}
            </select>
          </div>

          {selectedUser && (
            <div className="p-4 bg-gray-50 rounded">
              <h4 className="font-medium mb-2">Selected User Info:</h4>
              <p><strong>ID:</strong> {selectedUser.id}</p>
              <p><strong>Email:</strong> {selectedUser.email}</p>
              <p><strong>Name:</strong> {selectedUser.full_name || 'Not set'}</p>
              <p><strong>Current Phone:</strong> {selectedUser.phone || 'Not set'}</p>
              <p><strong>Role:</strong> {selectedUser.role}</p>
            </div>
          )}

          <div>
            <Label htmlFor="new-phone">New Phone Number</Label>
            <Input
              id="new-phone"
              value={newPhone}
              onChange={(e) => setNewPhone(e.target.value)}
              placeholder="************"
              disabled={!selectedUserId}
            />
          </div>

          <div className="flex gap-2">
            <Button 
              onClick={handleUpdate} 
              disabled={isLoading || !selectedUserId || !newPhone.trim()}
            >
              {isLoading ? 'Updating...' : 'Update Phone'}
            </Button>
            <Button 
              onClick={handleRefresh} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? 'Refreshing...' : 'Refresh Users'}
            </Button>
          </div>

          <div className="mt-4">
            <h4 className="font-medium mb-2">All Users:</h4>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {users.map(user => (
                <div key={user.id} className="p-2 border rounded text-sm">
                  <div><strong>{user.full_name || user.email}</strong></div>
                  <div>Phone: {user.phone || 'Not set'}</div>
                  <div className="text-gray-500 text-xs">ID: {user.id}</div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
