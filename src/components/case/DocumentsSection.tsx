import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, Download, FileText, File, Image, Archive, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCaseDocuments } from "@/hooks/useCaseDocuments";

interface DocumentsSectionProps {
  caseId?: string;
}

export const DocumentsSection = ({ caseId }: DocumentsSectionProps) => {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { documents, isLoading, uploading, uploadDocument, downloadDocument, deleteDocument } = useCaseDocuments(caseId);

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf":
        return <FileText className="w-5 h-5 text-primary" />;
      case "docx":
      case "doc":
        return <File className="w-5 h-5 text-primary" />;
      case "jpg":
      case "jpeg":
      case "png":
        return <Image className="w-5 h-5 text-primary" />;
      case "zip":
      case "rar":
        return <Archive className="w-5 h-5 text-primary" />;
      default:
        return <File className="w-5 h-5 text-primary" />;
    }
  };

  const handleUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    await uploadDocument(file);
    
    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDownload = async (document: any) => {
    await downloadDocument(document);
  };

  return (
    <Card className="card-professional">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            מסמכים
          </CardTitle>
          <Button 
            className="btn-professional flex items-center gap-2"
            onClick={handleUpload}
            disabled={uploading}
          >
            {uploading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Upload className="w-4 h-4" />}
            העלה מסמך
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            onChange={handleFileSelect}
            className="hidden"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.zip,.rar"
          />
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        ) : (
          <div className="space-y-3">
            {documents.map((document) => (
              <div
                key={document.id}
                className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/10 transition-colors duration-200"
              >
                <div className="flex items-center gap-3">
                  {getFileIcon(document.file_type || "file")}
                  <div>
                    <h4 className="font-medium text-foreground text-sm">
                      {document.filename}
                    </h4>
                    <div className="flex items-center gap-2 text-xs text-foreground">
                      <span>{document.file_size ? Math.round(document.file_size / 1024) + ' KB' : 'לא ידוע'}</span>
                      <span>•</span>
                      <span>{new Date(document.uploaded_at).toLocaleDateString('he-IL')}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleDownload(document)}
                    className="h-8 w-8 p-0"
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => deleteDocument(document.id, document.file_path)}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                  >
                    ×
                  </Button>
                </div>
              </div>
          ))}
          
            {documents.length === 0 && (
              <div className="text-center py-8 text-foreground">
                <FileText className="w-12 h-12 mx-auto mb-4" />
                <p>אין מסמכים עדיין</p>
                <p className="text-sm">העלה מסמכים כדי לנהל את התיק</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};