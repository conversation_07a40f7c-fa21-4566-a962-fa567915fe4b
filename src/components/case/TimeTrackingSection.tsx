import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Play,
  Pause,
  Plus,
  Clock,
  Timer,
  Trash2,
  Calendar
} from "lucide-react";
import { useCaseTimeEntries } from "@/hooks/useCaseTimeEntries";
import { format } from "date-fns";

interface TimeTrackingSectionProps {
  caseId: string;
  caseType?: {
    name: string;
  };
  onTimeEntryUpdated?: () => void; // Callback to trigger case data refetch
}

export const TimeTrackingSection = ({ caseId, caseType, onTimeEntryUpdated }: TimeTrackingSectionProps) => {
  const {
    timeEntries,
    isLoading,
    activeTimer,
    startTimer,
    stopTimer,
    addManualTimeEntry,
    deleteTimeEntry,
    getTotalTime,
  } = useCaseTimeEntries(caseId);

  const [isManualEntryOpen, setIsManualEntryOpen] = useState(false);
  const [timerDescription, setTimerDescription] = useState("");
  const [currentTime, setCurrentTime] = useState(new Date());

  // Manual entry form state
  const [manualEntry, setManualEntry] = useState({
    description: "",
    duration: "",
    date: format(new Date(), "yyyy-MM-dd"),
    start_time: "",
    end_time: "",
  });

  // Update current time every second for active timer display
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}ש ${mins}ד` : `${mins}ד`;
  };

  const formatTimerDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    const secs = Math.floor((minutes * 60) % 60);
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Wrapper functions to trigger case data refetch
  const handleStopTimer = async () => {
    await stopTimer();
    if (onTimeEntryUpdated) {
      onTimeEntryUpdated();
    }
  };

  const handleAddManualTimeEntry = async (entryData: any) => {
    const result = await addManualTimeEntry(entryData);
    if (result && onTimeEntryUpdated) {
      onTimeEntryUpdated();
    }
    return result;
  };

  const handleDeleteTimeEntry = async (entryId: string) => {
    await deleteTimeEntry(entryId);
    if (onTimeEntryUpdated) {
      onTimeEntryUpdated();
    }
  };



  const getActiveTimerDuration = () => {
    if (!activeTimer) return 0;
    return (currentTime.getTime() - activeTimer.startTime.getTime()) / 1000 / 60;
  };

  const handleStartTimer = () => {
    startTimer(timerDescription || "עבודה על התיק");
    setTimerDescription("");
  };

  const handleManualEntrySubmit = async () => {
    const duration = parseInt(manualEntry.duration);

    if (!duration || duration <= 0) {
      return;
    }

    const entryData = {
      case_id: caseId,
      description: manualEntry.description,
      duration,
      start_time: manualEntry.start_time ?
        new Date(`${manualEntry.date}T${manualEntry.start_time}`).toISOString() : undefined,
      end_time: manualEntry.end_time ?
        new Date(`${manualEntry.date}T${manualEntry.end_time}`).toISOString() : undefined,
    };

    const result = await handleAddManualTimeEntry(entryData);
    if (result) {
      setIsManualEntryOpen(false);
      setManualEntry({
        description: "",
        duration: "",
        date: format(new Date(), "yyyy-MM-dd"),
        start_time: "",
        end_time: "",
      });
    }
  };

  const totalMinutes = getTotalTime();

  return (
    <Card className="card-professional">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Timer className="w-5 h-5" />
          מעקב זמן
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Timer Controls */}
        <div className="flex flex-col gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span className="font-medium">טיימר פעיל</span>
            </div>
            {activeTimer && (
              <Badge variant="secondary" className="text-lg font-mono">
                {formatTimerDuration(getActiveTimerDuration())}
              </Badge>
            )}
          </div>
          
          {!activeTimer ? (
            <div className="flex gap-2">
              <Input
                placeholder="תיאור העבודה (אופציונלי)"
                value={timerDescription}
                onChange={(e) => setTimerDescription(e.target.value)}
                className="flex-1"
              />
              <Button onClick={handleStartTimer} className="flex items-center gap-2">
                <Play className="w-4 h-4" />
                התחל טיימר
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {activeTimer.description || "עבודה על התיק"}
              </span>
              <Button onClick={handleStopTimer} variant="destructive" className="flex items-center gap-2">
                <Pause className="w-4 h-4" />
                עצור טיימר
              </Button>
            </div>
          )}
        </div>

        {/* Summary Stats */}
        <div className="flex justify-center">
          <div className="text-center p-4 bg-primary/10 rounded-lg min-w-[200px]">
            <div className="text-3xl font-bold text-primary">{formatDuration(totalMinutes)}</div>
            <div className="text-sm text-muted-foreground">סה"כ זמן מושקע</div>
          </div>
        </div>

        {/* Manual Entry Button */}
        <Dialog open={isManualEntryOpen} onOpenChange={setIsManualEntryOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" className="w-full flex items-center gap-2">
              <Plus className="w-4 h-4" />
              הוסף רישום זמן ידני
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>רישום זמן ידני</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="description">תיאור</Label>
                <Textarea
                  id="description"
                  placeholder="תיאור העבודה שבוצעה"
                  value={manualEntry.description}
                  onChange={(e) => setManualEntry(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
              
              <div>
                <Label htmlFor="duration">זמן (דקות)</Label>
                <Input
                  id="duration"
                  type="number"
                  placeholder="60"
                  value={manualEntry.duration}
                  onChange={(e) => setManualEntry(prev => ({ ...prev, duration: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="date">תאריך</Label>
                <Input
                  id="date"
                  type="date"
                  value={manualEntry.date}
                  onChange={(e) => setManualEntry(prev => ({ ...prev, date: e.target.value }))}
                />
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="start_time">שעת התחלה (אופציונלי)</Label>
                  <Input
                    id="start_time"
                    type="time"
                    value={manualEntry.start_time}
                    onChange={(e) => setManualEntry(prev => ({ ...prev, start_time: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="end_time">שעת סיום (אופציונלי)</Label>
                  <Input
                    id="end_time"
                    type="time"
                    value={manualEntry.end_time}
                    onChange={(e) => setManualEntry(prev => ({ ...prev, end_time: e.target.value }))}
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleManualEntrySubmit} className="flex-1">
                  שמור רישום
                </Button>
                <Button variant="outline" onClick={() => setIsManualEntryOpen(false)}>
                  ביטול
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Time Entries List */}
        <div className="space-y-2">
          <h4 className="font-medium">רישומי זמן</h4>
          {isLoading ? (
            <div className="text-center py-4">טוען...</div>
          ) : timeEntries.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              אין רישומי זמן עדיין
            </div>
          ) : (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {timeEntries.map((entry) => (
                <div key={entry.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">{entry.description || "ללא תיאור"}</div>
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(entry.created_at), "dd/MM/yyyy HH:mm")}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{formatDuration(entry.duration)}</div>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleDeleteTimeEntry(entry.id)}
                    className="ml-2"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
