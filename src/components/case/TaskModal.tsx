import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { EnhancedDateTimePicker } from "@/components/ui/enhanced-datetime-picker";
import { useToast } from "@/hooks/use-toast";
import { useCaseTasks } from "@/hooks/useCaseTasks";
import { useUserManagement } from "@/hooks/useUserManagement";
import { Loader2 } from "lucide-react";

interface TaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  caseId?: string;
}

export const TaskModal = ({ isOpen, onClose, caseId }: TaskModalProps) => {
  const { toast } = useToast();
  const { addTask } = useCaseTasks(caseId);
  const { users } = useUserManagement();
  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    priority: "בינוני",
    deadline: "",
    assigned_to: "unassigned",
    reminders_enabled: true, // Default to enabled
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      toast({
        title: "שגיאה",
        description: "נא להזין כותרת למשימה",
        variant: "destructive",
      });
      return;
    }

    if (!caseId) {
      toast({
        title: "שגיאה",
        description: "מזהה התיק לא נמצא",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      await addTask({
        case_id: caseId,
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        priority: formData.priority,
        deadline: formData.deadline || undefined,
        assigned_to: formData.assigned_to === "unassigned" ? undefined : formData.assigned_to,
        reminders_enabled: formData.reminders_enabled,
      });

      setFormData({
        title: "",
        description: "",
        priority: "בינוני",
        deadline: "",
        assigned_to: "unassigned",
        reminders_enabled: true,
      });

      onClose();
    } catch (error) {
      // Error handled in hook
    } finally {
      setIsLoading(false);
    }
  };

  const priorityOptions = [
    "נמוך",
    "בינוני", 
    "גבוה",
    "קריטי"
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto hebrew-text" dir="rtl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">הוספת משימה חדשה</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 form-professional">
          <div className="space-y-2">
            <Label htmlFor="title">כותרת המשימה</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="כותרת המשימה..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">תיאור המשימה</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="תיאור המשימה..."
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priority">עדיפות</Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map((priority) => (
                    <SelectItem key={priority} value={priority}>
                      {priority}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="assigned_to">משתמש מוקצה</Label>
              <Select
                value={formData.assigned_to}
                onValueChange={(value) => setFormData(prev => ({ ...prev, assigned_to: value }))}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="בחר משתמש..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unassigned">ללא הקצאה</SelectItem>
                  {users.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.full_name} ({user.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <EnhancedDateTimePicker
              label="תאריך יעד"
              value={formData.deadline}
              onChange={(value) => setFormData(prev => ({ ...prev, deadline: value }))}
              placeholder="בחר תאריך ושעה..."
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border" dir="ltr">
            <Switch
              id="reminders"
              checked={formData.reminders_enabled}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, reminders_enabled: checked }))}
              disabled={isLoading}
            />
            <div className="space-y-1 text-right">
              <Label htmlFor="reminders" className="text-sm font-medium">
                תזכורות WhatsApp
              </Label>
              <p className="text-xs text-gray-600">
                קבל תזכורות 7, 3 ויום אחד לפני המועד הסופי
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              ביטול
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="btn-professional min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin ml-2" />
                  יוצר...
                </>
              ) : (
                "הוסף משימה"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};