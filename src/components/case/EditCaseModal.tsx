import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { useCases } from "@/hooks/useCases";
import { useLeads } from "@/hooks/useLeads";
import { useToast } from "@/hooks/use-toast";
import { useUserManagement } from "@/hooks/useUserManagement";

interface EditCaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  caseData: any;
  onUpdate: (updatedCase: any) => void;
}

export const EditCaseModal = ({ isOpen, onClose, caseData, onUpdate }: EditCaseModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    case_type_id: "",
    value: "",
    lead_id: "",
    deadline: null as Date | null,
    assigned_user_id: "",
  });

  const { caseTypes, updateCase } = useCases();
  const { leads } = useLeads();
  const { toast } = useToast();
  const { users } = useUserManagement();

  useEffect(() => {
    if (caseData && isOpen) {
      setFormData({
        title: caseData.title || "",
        description: caseData.description || "",
        case_type_id: caseData.case_type_id || "",
        value: caseData.value?.toString() || "",
        lead_id: caseData.lead_id || "",
        deadline: caseData.deadline ? new Date(caseData.deadline) : null,
        assigned_user_id: caseData.assigned_user_id || "",
      });
    }
  }, [caseData, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Validate required fields
    if (!formData.assigned_user_id) {
      toast({
        title: "שגיאה",
        description: "יש לבחור משתמש מוקצה",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    try {
      const updateData = {
        title: formData.title,
        description: formData.description,
        case_type_id: formData.case_type_id && formData.case_type_id !== "none" ? formData.case_type_id : null,
        value: formData.value ? parseFloat(formData.value) : null,
        lead_id: formData.lead_id && formData.lead_id !== "none" ? formData.lead_id : null,
        deadline: formData.deadline ? formData.deadline.toISOString() : null,
        assigned_user_id: formData.assigned_user_id,
      };

      const result = await updateCase(caseData.id, updateData);
      
      if (result) {
        // Update the case data with the new information
        const updatedCase = {
          ...caseData,
          ...updateData,
          case_type: formData.case_type_id && formData.case_type_id !== "none" ?
            caseTypes.find(ct => ct.id === formData.case_type_id) : null,
          lead: formData.lead_id && formData.lead_id !== "none" ?
            leads.find(l => l.id === formData.lead_id) : null,
        };
        
        onUpdate(updatedCase);
        onClose();
        
        toast({
          title: "הצלחה",
          description: "פרטי התיק עודכנו בהצלחה",
        });
      }
    } catch (error) {
      console.error('Error updating case:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בעדכון פרטי התיק",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>עריכת פרטי התיק</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div>
            <Label htmlFor="title">שם התיק *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="הכנס שם התיק"
              required
            />
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">תיאור התיק</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="תיאור מפורט של התיק"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Case Type */}
            <div>
              <Label htmlFor="case_type">סוג התיק</Label>
              <Select
                value={formData.case_type_id}
                onValueChange={(value) => setFormData(prev => ({ ...prev, case_type_id: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="בחר סוג תיק" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">ללא סוג תיק</SelectItem>
                  {caseTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.name} ({type.hourly_rate}₪/שעה)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Value */}
            <div>
              <Label htmlFor="value">ערך התיק (₪)</Label>
              <Input
                id="value"
                type="number"
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                placeholder="0"
                min="0"
                step="0.01"
              />
            </div>
          </div>

          {/* Lead */}
          <div>
            <Label htmlFor="lead">לקוח</Label>
            <Select
              value={formData.lead_id}
              onValueChange={(value) => setFormData(prev => ({ ...prev, lead_id: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="בחר לקוח" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">ללא לקוח</SelectItem>
                {leads.map((lead) => (
                  <SelectItem key={lead.id} value={lead.id}>
                    {lead.full_name} ({lead.phone})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Assigned User */}
          <div>
            <Label>משתמש מוקצה *</Label>
            <Select
              value={formData.assigned_user_id}
              onValueChange={(value) => setFormData(prev => ({ ...prev, assigned_user_id: value }))}
              required
            >
              <SelectTrigger className={!formData.assigned_user_id ? 'border-red-300 focus:border-red-500' : ''}>
                <SelectValue placeholder="בחר משתמש מוקצה" />
              </SelectTrigger>
              <SelectContent>
                {users.map((user) => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.full_name || user.email}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {!formData.assigned_user_id && (
              <p className="text-sm text-red-600 mt-1">שדה חובה</p>
            )}
          </div>

          {/* Deadline */}
          <div>
            <Label>דדליין</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.deadline ? (
                    format(formData.deadline, "dd/MM/yyyy")
                  ) : (
                    <span>בחר תאריך</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={formData.deadline}
                  onSelect={(date) => setFormData(prev => ({ ...prev, deadline: date }))}
                  initialFocus
                />
                {formData.deadline && (
                  <div className="p-3 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setFormData(prev => ({ ...prev, deadline: null }))}
                      className="w-full"
                    >
                      הסר דדליין
                    </Button>
                  </div>
                )}
              </PopoverContent>
            </Popover>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading && <Loader2 className="w-4 h-4 animate-spin mr-2" />}
              שמור שינויים
            </Button>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
              ביטול
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
