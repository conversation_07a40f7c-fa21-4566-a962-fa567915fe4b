import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useLeads } from "@/hooks/useLeads";
import { useCases, Case, CaseType } from "@/hooks/useCases";
import { useUserManagement } from "@/hooks/useUserManagement";

interface CaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (case_: Case) => void;
  leadId?: string;
  caseTypes: CaseType[];
}

export const CaseModal = ({ isOpen, onClose, onSuccess, leadId, caseTypes }: CaseModalProps) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [caseTypeId, setCaseTypeId] = useState("");
  const [selectedLeadId, setSelectedLeadId] = useState(leadId || "");
  const [value, setValue] = useState("");
  const [deadline, setDeadline] = useState("");
  const [assignedUserId, setAssignedUserId] = useState("");

  const { leads } = useLeads();
  const { addCase } = useCases();
  const { users } = useUserManagement();

  useEffect(() => {
    if (leadId) {
      setSelectedLeadId(leadId);
    }
  }, [leadId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!assignedUserId) {
      alert("יש לבחור משתמש מוקצה");
      return;
    }

    const caseData = {
      title,
      description: description || undefined,
      case_type_id: caseTypeId || undefined,
      lead_id: selectedLeadId || undefined,
      value: value ? parseFloat(value) : undefined,
      deadline: deadline || undefined,
      assigned_user_id: assignedUserId,
      status: "בקליטה",
    };

    const newCase = await addCase(caseData);
    if (newCase) {
      onSuccess?.(newCase);
      handleClose();
    }
  };

  const handleClose = () => {
    setTitle("");
    setDescription("");
    setCaseTypeId("");
    setSelectedLeadId(leadId || "");
    setValue("");
    setDeadline("");
    setAssignedUserId("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>צור תיק חדש</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">שם התיק *</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="הכנס שם התיק"
              required
            />
          </div>

          <div>
            <Label htmlFor="lead">לקוח</Label>
            <Select value={selectedLeadId} onValueChange={setSelectedLeadId}>
              <SelectTrigger>
                <SelectValue placeholder="בחר לקוח" />
              </SelectTrigger>
              <SelectContent>
                {leads.map((lead) => (
                  <SelectItem key={lead.id} value={lead.id}>
                    {lead.full_name} - {lead.phone}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="caseType">סוג התיק</Label>
            <Select value={caseTypeId} onValueChange={setCaseTypeId}>
              <SelectTrigger>
                <SelectValue placeholder="בחר סוג תיק" />
              </SelectTrigger>
              <SelectContent>
                {caseTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="value">ערך התיק (₪)</Label>
            <Input
              id="value"
              type="number"
              step="0.01"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder="0.00"
            />
          </div>

          <div>
            <Label htmlFor="deadline">תאריך יעד</Label>
            <Input
              id="deadline"
              type="datetime-local"
              value={deadline}
              onChange={(e) => setDeadline(e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="assigned-user">משתמש מוקצה *</Label>
            <Select value={assignedUserId} onValueChange={setAssignedUserId} required>
              <SelectTrigger className={!assignedUserId ? 'border-red-300 focus:border-red-500' : ''}>
                <SelectValue placeholder="בחר משתמש מוקצה" />
              </SelectTrigger>
              <SelectContent>
                {users.map(user => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.full_name || user.email}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {!assignedUserId && (
              <p className="text-sm text-red-600 mt-1">שדה חובה</p>
            )}
          </div>

          <div>
            <Label htmlFor="description">תיאור</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="תיאור התיק..."
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              ביטול
            </Button>
            <Button type="submit">צור תיק</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};