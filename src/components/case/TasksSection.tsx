import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Plus, Play, Pause, Clock, Timer, Loader2, Edit, Calendar, User, Bell, <PERSON>Off, Trash2 } from "lucide-react";
import { TaskModal } from "./TaskModal";
import { EditTaskModal } from "./EditTaskModal";
import { useCaseTasks, CaseTask } from "@/hooks/useCaseTasks";
import { useCaseTimeEntries } from "@/hooks/useCaseTimeEntries";
import { format } from "date-fns";
import { he } from "date-fns/locale";

interface TasksSectionProps {
  caseId?: string;
  onTaskUpdated?: () => void; // Callback to trigger case data refetch
}

export const TasksSection = ({ caseId, onTaskUpdated }: TasksSectionProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<CaseTask | null>(null);
  const { tasks, isLoading: tasksLoading, toggleTaskComplete, deleteTask } = useCaseTasks(caseId);
  const { startTimer, stopTimer, activeTimer, isLoading: timerLoading } = useCaseTimeEntries(caseId);

  const handleToggleTimer = async (taskId: string) => {
    if (activeTimer && activeTimer.id === taskId) {
      await stopTimer();
    } else {
      await startTimer(`עבודה על משימה`);
    }
  };

  const handleEditTask = (task: CaseTask) => {
    setSelectedTask(task);
    setIsEditModalOpen(true);
  };

  const handleDeleteTask = async (taskId: string, taskTitle: string) => {
    if (window.confirm(`האם אתה בטוח שברצונך למחוק את המשימה "${taskTitle}"?`)) {
      await deleteTask(taskId);
      // Trigger case data refetch if callback provided
      if (onTaskUpdated) {
        onTaskUpdated();
      }
    }
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setSelectedTask(null);
  };

  const formatDeadline = (deadline: string) => {
    try {
      const date = new Date(deadline);
      return format(date, "dd/MM/yyyy HH:mm", { locale: he });
    } catch {
      return deadline;
    }
  };

  const getDeadlineStatus = (deadline: string) => {
    if (!deadline) return null;

    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diffInHours = (deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 0) return 'overdue';
    if (diffInHours < 24) return 'urgent';
    if (diffInHours < 72) return 'soon';
    return 'normal';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "הושלם":
        return "bg-success/10 text-success border-success/20";
      case "בתהליך":
        return "bg-primary/10 text-primary border-primary/20";
      case "ממתין":
        return "bg-warning/10 text-warning border-warning/20";
      default:
        return "bg-muted text-muted-foreground border-border";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'קריטי': return 'text-red-600';
      case 'גבוה': return 'text-orange-600';
      case 'בינוני': return 'text-yellow-600';
      case 'נמוך': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <Card className="card-professional">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Timer className="w-5 h-5" />
            משימות
          </CardTitle>
          <Button 
            className="btn-professional flex items-center gap-2"
            onClick={() => setIsModalOpen(true)}
          >
            <Plus className="w-4 h-4" />
            הוסף משימה חדשה
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {tasksLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        ) : (
          <div className="space-y-4">
            {tasks.map((task) => (
              <div
                key={task.id}
                className="p-4 border border-border rounded-lg hover:bg-muted/10 transition-colors duration-200"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium text-lg">{task.title}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                        {task.status}
                      </span>
                      <span className={`text-sm font-medium ${getPriorityColor(task.priority)}`}>
                        {task.priority}
                      </span>
                    </div>

                    {task.description && (
                      <p className="text-sm text-muted-foreground mb-2">{task.description}</p>
                    )}

                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      {task.deadline && (
                        <div className={`flex items-center gap-1 ${
                          getDeadlineStatus(task.deadline) === 'overdue' ? 'text-red-600 font-medium' :
                          getDeadlineStatus(task.deadline) === 'urgent' ? 'text-orange-600 font-medium' :
                          getDeadlineStatus(task.deadline) === 'soon' ? 'text-yellow-600 font-medium' :
                          'text-muted-foreground'
                        }`}>
                          <Calendar className="w-4 h-4" />
                          <span>{formatDeadline(task.deadline)}</span>
                          {getDeadlineStatus(task.deadline) === 'overdue' && <span className="text-xs">(פג תוקף)</span>}
                          {getDeadlineStatus(task.deadline) === 'urgent' && <span className="text-xs">(דחוף)</span>}
                        </div>
                      )}

                      {task.assigned_to && (
                        <div className="flex items-center gap-1">
                          <User className="w-4 h-4" />
                          <span>מוקצה</span>
                        </div>
                      )}

                      {task.deadline && (
                        <div className={`flex items-center gap-1 ${
                          task.reminders_enabled ? 'text-green-600' : 'text-gray-400'
                        }`}>
                          {task.reminders_enabled ? (
                            <Bell className="w-4 h-4" />
                          ) : (
                            <BellOff className="w-4 h-4" />
                          )}
                          <span className="text-xs">
                            {task.reminders_enabled ? 'תזכורות פעילות' : 'תזכורות כבויות'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleEditTask(task)}
                      className="shrink-0"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDeleteTask(task.id, task.title)}
                      className="shrink-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-foreground">
                    <Clock className="w-4 h-4" />
                    <span>זמן רשום: 0 דקות</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant={activeTimer?.id === task.id ? "destructive" : "default"}
                      onClick={() => handleToggleTimer(task.id)}
                      className="flex items-center gap-1"
                      disabled={timerLoading}
                    >
                      {activeTimer?.id === task.id ? (
                        <>
                          <Pause className="w-4 h-4" />
                          עצור טיימר
                        </>
                      ) : (
                        <>
                          <Play className="w-4 h-4" />
                          התחל טיימר
                        </>
                      )}
                    </Button>
                    
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => toggleTaskComplete(task.id)}
                    >
                      {task.status === "הושלם" ? "בטל השלמה" : "השלם"}
                    </Button>
                  </div>
                </div>
              </div>
          ))}
          
            {tasks.length === 0 && (
              <div className="text-center py-8 text-foreground">
                <Timer className="w-12 h-12 mx-auto mb-4" />
                <p>אין משימות עדיין</p>
                <p className="text-sm">הוסף משימה חדשה כדי להתחיל לעקוב אחר הזמן</p>
              </div>
            )}
          </div>
        )}
      </CardContent>

      <TaskModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        caseId={caseId}
      />

      <EditTaskModal
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        task={selectedTask}
        caseId={caseId}
        onTaskUpdated={onTaskUpdated}
      />
    </Card>
  );
};