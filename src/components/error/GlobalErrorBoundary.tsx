import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Al<PERSON>Triangle, RotateCcw, Home, Bug } from 'lucide-react';
import { toast } from 'sonner';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

/**
 * Global Error Boundary with enhanced error reporting and recovery
 */
export class GlobalErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Generate unique error ID for tracking
    const errorId = `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 Global Error Boundary caught an error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId
    });

    // Update state with error info
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Report to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo);
    }

    // Show user-friendly toast notification
    toast.error('אירעה שגיאה במערכת', {
      description: 'הצוות הטכני קיבל התראה ויטפל בבעיה',
      duration: 5000
    });
  }

  private reportError(error: Error, errorInfo: ErrorInfo) {
    // TODO: Integrate with monitoring service (Sentry, LogRocket, etc.)
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      errorId: this.state.errorId
    };

    // Send to monitoring service
    console.log('📊 Error report:', errorReport);
  }

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: undefined, 
      errorInfo: undefined,
      errorId: undefined 
    });
  };

  handleReportBug = () => {
    const bugReport = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Copy to clipboard for easy reporting
    navigator.clipboard.writeText(JSON.stringify(bugReport, null, 2));
    toast.success('פרטי השגיאה הועתקו ללוח');
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center p-4 bg-background">
          <Card className="w-full max-w-lg border-destructive/20">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-destructive/10 rounded-full">
                  <AlertTriangle className="h-8 w-8 text-destructive" />
                </div>
              </div>
              <CardTitle className="text-xl font-semibold text-foreground">
                אירעה שגיאה במערכת
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-2">
                מזהה שגיאה: {this.state.errorId}
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground text-center">
                אירעה שגיאה לא צפויה במערכת. אנא נסה שוב או חזור לעמוד הבית.
                אם הבעיה נמשכת, אנא דווח על השגיאה.
              </p>
              
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="p-3 bg-muted rounded-md border">
                  <p className="text-xs font-semibold mb-2 text-destructive">
                    Development Error Details:
                  </p>
                  <p className="text-xs font-mono text-destructive break-all">
                    {this.state.error.message}
                  </p>
                  {this.state.error.stack && (
                    <details className="mt-2">
                      <summary className="text-xs cursor-pointer text-muted-foreground">
                        Stack Trace
                      </summary>
                      <pre className="text-xs mt-1 text-muted-foreground whitespace-pre-wrap">
                        {this.state.error.stack}
                      </pre>
                    </details>
                  )}
                </div>
              )}
              
              <div className="flex gap-2 justify-center flex-wrap">
                <Button
                  onClick={this.handleRetry}
                  className="flex items-center gap-2"
                  size="sm"
                >
                  <RotateCcw className="h-4 w-4" />
                  נסה שוב
                </Button>
                <Button
                  onClick={() => window.location.href = '/'}
                  variant="outline"
                  className="flex items-center gap-2"
                  size="sm"
                >
                  <Home className="h-4 w-4" />
                  עמוד הבית
                </Button>
                <Button
                  onClick={this.handleReportBug}
                  variant="secondary"
                  className="flex items-center gap-2"
                  size="sm"
                >
                  <Bug className="h-4 w-4" />
                  דווח על שגיאה
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook for programmatic error reporting
 */
export const useErrorReporting = () => {
  const reportError = (error: Error, context?: string) => {
    console.error('🚨 Manual error report:', { error, context });
    
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to monitoring service
    }
    
    toast.error('אירעה שגיאה', {
      description: context || 'פרטים נוספים נשלחו לצוות הטכני'
    });
  };

  return { reportError };
};
