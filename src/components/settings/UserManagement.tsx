import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Users, Plus, Shield, User, Mail, MoreHorizontal, Trash2, User<PERSON>heck, Edit } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useUserManagement, CreateUserData, CompanyUser } from '@/hooks/useUserManagement';

export const UserManagement: React.FC = () => {
  const {
    users,
    isLoading,
    isCreating,
    createUser,
    updateUser,
    updateUserRole,
    deactivateUser,
    sendPasswordReset,
    canManageUsers,
    getUserStats
  } = useUserManagement();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<CompanyUser | null>(null);
  const [newUser, setNewUser] = useState<CreateUserData>({
    email: '',
    full_name: '',
    phone: '',
    role: 'user',
    password: '' // Will be auto-generated if empty
  });

  const stats = getUserStats();

  const handleCreateUser = async () => {
    if (!newUser.email || !newUser.full_name) {
      return;
    }

    const success = await createUser(newUser);
    if (success) {
      setIsCreateDialogOpen(false);
      setNewUser({
        email: '',
        full_name: '',
        phone: '',
        role: 'user',
        password: ''
      });
    }
  };



  const handleRoleChange = async (userId: string, newRole: 'company_admin' | 'user') => {
    await updateUserRole(userId, newRole);
  };

  const handleDeactivateUser = async (userId: string) => {
    await deactivateUser(userId);
  };

  const handleSendPasswordReset = async (email: string) => {
    await sendPasswordReset(email);
  };

  const getRoleBadge = (role: string) => {
    if (role === 'super_admin') {
      return (
        <Badge variant="default" className="bg-purple-100 text-purple-800">
          <Shield className="w-3 h-3 mr-1" />
          סופר אדמין
        </Badge>
      );
    }
    return role === 'company_admin' ? (
      <Badge variant="default" className="bg-blue-100 text-blue-800">
        <Shield className="w-3 h-3 mr-1" />
        מנהל
      </Badge>
    ) : (
      <Badge variant="secondary">
        <User className="w-3 h-3 mr-1" />
        משתמש
      </Badge>
    );
  };

  const getStatusBadge = (user: any) => {
    if (user.status === 'pending') {
      return <Badge variant="outline" className="bg-blue-100 text-blue-800">ממתין לאישור</Badge>;
    }
    return user.is_active ? (
      <Badge variant="default" className="bg-green-100 text-green-800">פעיל</Badge>
    ) : (
      <Badge variant="destructive">לא פעיל</Badge>
    );
  };

  if (!canManageUsers()) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2" dir="rtl">
            <Users className="h-5 w-5" />
            ניהול משתמשים
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-muted-foreground" dir="rtl">
            <Shield className="h-8 w-8 mx-auto mb-2" />
            <p>אין לך הרשאות לניהול משתמשים</p>
            <p className="text-sm mt-2">רק מנהלי חברה יכולים לנהל משתמשים</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2" dir="rtl">
          <Users className="h-5 w-5" />
          ניהול משתמשים
        </CardTitle>
        <CardDescription dir="rtl">
          ניהול משתמשי החברה והרשאותיהם
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold text-primary">{stats.total}</div>
            <div className="text-sm text-muted-foreground">סה"כ משתמשים</div>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <div className="text-sm text-muted-foreground">פעילים</div>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{stats.admins}</div>
            <div className="text-sm text-muted-foreground">מנהלים</div>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{stats.users}</div>
            <div className="text-sm text-muted-foreground">משתמשים רגילים</div>
          </div>
        </div>

        {/* Create User Button */}
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium" dir="rtl">רשימת משתמשים</h3>
          {canManageUsers() && (
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  הוסף משתמש
                </Button>
              </DialogTrigger>
            <DialogContent className="sm:max-w-md" dir="rtl">
              <DialogHeader>
                <DialogTitle>יצירת משתמש חדש</DialogTitle>
                <DialogDescription>
                  צור משתמש חדש עם גישה מיידית למערכת
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="email">כתובת אימייל</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="full_name">שם מלא</Label>
                  <Input
                    id="full_name"
                    value={newUser.full_name}
                    onChange={(e) => setNewUser(prev => ({ ...prev, full_name: e.target.value }))}
                    placeholder="שם מלא"
                  />
                </div>
                <div>
                  <Label htmlFor="phone">מספר טלפון (אופציונלי)</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={newUser.phone}
                    onChange={(e) => setNewUser(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="************"
                  />
                </div>
                <div>
                  <Label htmlFor="password">סיסמה (אופציונלי)</Label>
                  <Input
                    id="password"
                    type="password"
                    value={newUser.password}
                    onChange={(e) => setNewUser(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="השאר ריק ליצירת סיסמה אוטומטית"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    אם לא תזין סיסמה, תיווצר סיסמה זמנית אוטומטית
                  </p>
                </div>
                <div>
                  <Label htmlFor="role">תפקיד</Label>
                  <Select value={newUser.role} onValueChange={(value: 'company_admin' | 'user') => setNewUser(prev => ({ ...prev, role: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">משתמש רגיל</SelectItem>
                      <SelectItem value="company_admin">מנהל</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
                  <p>המשתמש יוכל להתחבר מיד עם הפרטים שיוצגו לאחר היצירה.</p>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  ביטול
                </Button>
                <Button onClick={handleCreateUser} disabled={isCreating}>
                  {isCreating ? 'יוצר משתמש...' : 'צור משתמש'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          )}

          {/* Edit User Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="sm:max-w-md" dir="rtl">
              <DialogHeader>
                <DialogTitle>עריכת פרטי משתמש</DialogTitle>
                <DialogDescription>
                  עדכן את פרטי המשתמש
                </DialogDescription>
              </DialogHeader>
              {editingUser && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="edit-email">כתובת אימייל</Label>
                    <Input
                      id="edit-email"
                      type="email"
                      value={editingUser.email}
                      onChange={(e) => setEditingUser({...editingUser, email: e.target.value})}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-name">שם מלא</Label>
                    <Input
                      id="edit-name"
                      value={editingUser.full_name || ''}
                      onChange={(e) => setEditingUser({...editingUser, full_name: e.target.value})}
                      placeholder="שם מלא"
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-phone">מספר טלפון</Label>
                    <Input
                      id="edit-phone"
                      type="tel"
                      value={editingUser.phone || ''}
                      onChange={(e) => setEditingUser({...editingUser, phone: e.target.value})}
                      placeholder="************"
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-role">תפקיד</Label>
                    <Select
                      value={editingUser.role}
                      onValueChange={(value: 'company_admin' | 'user') => setEditingUser({...editingUser, role: value})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">משתמש רגיל</SelectItem>
                        <SelectItem value="company_admin">מנהל</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
                    <p>ניתן לעדכן את השם, התפקיד ובעתיד גם את האימייל.</p>
                  </div>
                </div>
              )}
              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsEditDialogOpen(false);
                  setEditingUser(null);
                }}>
                  ביטול
                </Button>
                <Button onClick={async () => {
                  if (editingUser) {
                    // Find the original user to compare changes
                    const originalUser = users.find(u => u.id === editingUser.id);

                    // Update user details if name, email, or phone changed
                    if (originalUser) {
                      const nameChanged = originalUser.full_name !== editingUser.full_name;
                      const emailChanged = originalUser.email !== editingUser.email;
                      const phoneChanged = (originalUser.phone || '') !== (editingUser.phone || '');



                      if (nameChanged || emailChanged || phoneChanged) {
                        const updates: { full_name?: string; email?: string; phone?: string } = {};
                        if (nameChanged) updates.full_name = editingUser.full_name || '';
                        if (emailChanged) updates.email = editingUser.email;
                        if (phoneChanged) updates.phone = editingUser.phone || '';


                        const success = await updateUser(editingUser.id, updates);

                        if (!success) {
                          return; // Don't close dialog if update failed
                        }
                      } else {

                      }

                      // Update role if changed
                      if (originalUser.role !== editingUser.role) {
                        const roleSuccess = await handleRoleChange(editingUser.id, editingUser.role);
                        if (!roleSuccess) {
                          return; // Don't close dialog if role update failed
                        }
                      }
                    }

                    // Close dialog and refresh data
                    setIsEditDialogOpen(false);
                    setEditingUser(null);

                    // Force refresh after a short delay
                    setTimeout(() => {
                      fetchUsers();
                    }, 1000);
                  }
                }}>
                  שמור שינויים
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Users Table */}
        {isLoading ? (
          <div className="text-center py-6">טוען...</div>
        ) : users.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground" dir="rtl">
            <Users className="h-8 w-8 mx-auto mb-2" />
            <p>אין משתמשים רשומים</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">שם</TableHead>
                <TableHead className="text-right">אימייל</TableHead>
                <TableHead className="text-right">טלפון</TableHead>
                <TableHead className="text-right">תפקיד</TableHead>
                <TableHead className="text-right">סטטוס</TableHead>
                <TableHead className="text-right">כניסה אחרונה</TableHead>
                <TableHead className="text-right">פעולות</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">{user.full_name || 'לא צוין'}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.phone || 'לא צוין'}</TableCell>
                  <TableCell>{getRoleBadge(user.role)}</TableCell>
                  <TableCell>{getStatusBadge(user)}</TableCell>
                  <TableCell>
                    {user.last_sign_in_at 
                      ? new Date(user.last_sign_in_at).toLocaleDateString('he-IL')
                      : 'מעולם לא נכנס'
                    }
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {canManageUsers() && (
                          <DropdownMenuItem onClick={() => {
                            setEditingUser(user);
                            setIsEditDialogOpen(true);
                          }}>
                            <Edit className="h-4 w-4 mr-2" />
                            ערוך פרטים
                          </DropdownMenuItem>
                        )}
                        {user.status !== 'pending' && (
                          <>
                            {canManageUsers() && user.role !== 'super_admin' && (
                              <DropdownMenuItem onClick={() => handleRoleChange(user.id, user.role === 'company_admin' ? 'user' : 'company_admin')}>
                                <UserCheck className="h-4 w-4 mr-2" />
                                {user.role === 'company_admin' ? 'הפוך למשתמש רגיל' : 'הפוך למנהל'}
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={() => handleSendPasswordReset(user.email)}>
                              <Mail className="h-4 w-4 mr-2" />
                              שלח איפוס סיסמה
                            </DropdownMenuItem>
                          </>
                        )}
                        {canManageUsers() && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                {user.status === 'pending' ? 'בטל הזמנה' : 'הסר מהחברה'}
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>האם אתה בטוח?</AlertDialogTitle>
                              <AlertDialogDescription>
                                {user.status === 'pending'
                                  ? 'פעולה זו תבטל את ההזמנה שנשלחה למשתמש.'
                                  : 'פעולה זו תמחק את המשתמש לחלוטין מהמערכת. ניתן יהיה ליצור אותו מחדש לאחר מכן.'
                                }
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>ביטול</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDeactivateUser(user.id)}>
                                {user.status === 'pending' ? 'בטל הזמנה' : 'מחק משתמש'}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};
