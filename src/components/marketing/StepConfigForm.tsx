import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useCases } from '@/hooks/useCases';

interface StepConfigFormProps {
  stepType: 'send_whatsapp' | 'wait' | 'update_lead_status' | 'update_case_status' | 'create_case';
  config: any;
  onChange: (config: any) => void;
  triggerType: 'lead_status_change' | 'case_status_change';
}

export const StepConfigForm = ({ stepType, config, onChange, triggerType }: StepConfigFormProps) => {
  const { caseTypes } = useCases();

  const leadStatuses = [
    'ליד חדש',
    'צריך פולואפ',
    'לקוח סגור',
    'לא ענה',
    'לא מעוניין',
    'לא מתאים'
  ];

  const caseStatuses = [
    'בקליטה',
    'פתוח',
    'סגור'
  ];

  const waitUnits = [
    { value: 'minutes', label: 'דקות' },
    { value: 'hours', label: 'שעות' },
    { value: 'days', label: 'ימים' }
  ];

  const updateConfig = (key: string, value: any) => {
    onChange({ ...config, [key]: value });
  };

  const renderWhatsAppConfig = () => (
    <div className="space-y-4">
      <div>
        <Label>נמען ההודעה *</Label>
        <RadioGroup
          value={config.recipient || 'lead'}
          onValueChange={(value) => updateConfig('recipient', value)}
          className="mt-2"
        >
          <div className="flex items-center space-x-2 space-x-reverse">
            <RadioGroupItem value="lead" id="recipient-lead" />
            <Label htmlFor="recipient-lead">שלח ללקוח</Label>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <RadioGroupItem value="assigned_user" id="recipient-user" />
            <Label htmlFor="recipient-user">שלח למשתמש המוקצה</Label>
          </div>
        </RadioGroup>
        {!config.recipient && (
          <p className="text-sm text-red-600 mt-1">שדה חובה</p>
        )}
      </div>

      <div>
        <Label htmlFor="whatsapp-message">הודעה *</Label>
        <Textarea
          id="whatsapp-message"
          value={config.message || ''}
          onChange={(e) => updateConfig('message', e.target.value)}
          placeholder="הכנס את ההודעה שתישלח"
          rows={4}
          className={!config.message?.trim() ? 'border-red-300 focus:border-red-500' : ''}
          required
        />
        {!config.message?.trim() && (
          <p className="text-sm text-red-600 mt-1">שדה חובה</p>
        )}
        <div className="mt-2">
          <p className="text-sm text-muted-foreground mb-2">משתנים זמינים:</p>
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className="text-xs">{'{lead_name}'}</Badge>
            <Badge variant="outline" className="text-xs">{'{lead_phone}'}</Badge>
            <Badge variant="outline" className="text-xs">{'{lead_email}'}</Badge>
            <Badge variant="outline" className="text-xs">{'{lead_status}'}</Badge>
          </div>
        </div>
      </div>
    </div>
  );

  const renderWaitConfig = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="wait-duration">משך זמן *</Label>
          <Input
            id="wait-duration"
            type="number"
            min="1"
            value={config.duration || ''}
            onChange={(e) => updateConfig('duration', parseInt(e.target.value) || 1)}
            placeholder="1"
            className={(!config.duration || config.duration < 1) ? 'border-red-300 focus:border-red-500' : ''}
            required
          />
          {(!config.duration || config.duration < 1) && (
            <p className="text-sm text-red-600 mt-1">שדה חובה</p>
          )}
        </div>
        <div>
          <Label htmlFor="wait-unit">יחידת זמן *</Label>
          <Select value={config.unit || 'hours'} onValueChange={(value) => updateConfig('unit', value)}>
            <SelectTrigger className={!config.unit ? 'border-red-300 focus:border-red-500' : ''}>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {waitUnits.map(unit => (
                <SelectItem key={unit.value} value={unit.value}>{unit.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          {!config.unit && (
            <p className="text-sm text-red-600 mt-1">שדה חובה</p>
          )}
        </div>
      </div>
    </div>
  );

  const renderUpdateLeadStatusConfig = () => (
    <div className="space-y-4">
      <div>
        <Label htmlFor="new-lead-status">סטטוס חדש *</Label>
        <Select value={config.new_status || ''} onValueChange={(value) => updateConfig('new_status', value)}>
          <SelectTrigger>
            <SelectValue placeholder="בחר סטטוס חדש" />
          </SelectTrigger>
          <SelectContent>
            {leadStatuses.map(status => (
              <SelectItem key={status} value={status}>{status}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );

  const renderUpdateCaseStatusConfig = () => (
    <div className="space-y-4">
      <div>
        <Label htmlFor="new-case-status">סטטוס חדש *</Label>
        <Select value={config.new_status || ''} onValueChange={(value) => updateConfig('new_status', value)}>
          <SelectTrigger>
            <SelectValue placeholder="בחר סטטוס חדש" />
          </SelectTrigger>
          <SelectContent>
            {caseStatuses.map(status => (
              <SelectItem key={status} value={status}>{status}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );

  const renderCreateCaseConfig = () => (
    <div className="space-y-4">
      <div>
        <Label htmlFor="case-title">כותרת התיק *</Label>
        <Input
          id="case-title"
          value={config.title || ''}
          onChange={(e) => updateConfig('title', e.target.value)}
          placeholder="הכנס כותרת לתיק"
          className={!config.title?.trim() ? 'border-red-300 focus:border-red-500' : ''}
          required
        />
        {!config.title?.trim() && (
          <p className="text-sm text-red-600 mt-1">שדה חובה</p>
        )}
      </div>

      <div>
        <Label htmlFor="case-type">סוג תיק *</Label>
        <Select value={config.case_type_id || ''} onValueChange={(value) => updateConfig('case_type_id', value)}>
          <SelectTrigger className={!config.case_type_id ? 'border-red-300 focus:border-red-500' : ''}>
            <SelectValue placeholder="בחר סוג תיק" />
          </SelectTrigger>
          <SelectContent>
            {caseTypes.map(type => (
              <SelectItem key={type.id} value={type.id}>{type.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        {!config.case_type_id && (
          <p className="text-sm text-red-600 mt-1">שדה חובה</p>
        )}
      </div>

      <div>
        <Label htmlFor="case-status">סטטוס התיק</Label>
        <Select value={config.status || 'בקליטה'} onValueChange={(value) => updateConfig('status', value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {caseStatuses.map(status => (
              <SelectItem key={status} value={status}>{status}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="case-value">ערך התיק (₪)</Label>
          <Input
            id="case-value"
            type="number"
            min="0"
            value={config.value || ''}
            onChange={(e) => updateConfig('value', parseFloat(e.target.value) || null)}
            placeholder="0"
          />
        </div>
        <div>
          <Label htmlFor="case-deadline">דדליין</Label>
          <Input
            id="case-deadline"
            type="date"
            value={config.deadline || ''}
            onChange={(e) => updateConfig('deadline', e.target.value || null)}
          />
        </div>
      </div>

      <Card className="bg-muted/50">
        <CardContent className="p-3">
          <p className="text-sm text-muted-foreground">
            <strong>הערה:</strong> הליד המשויך לתיק יילקח אוטומטית מהטריגר שהפעיל את הזרימה.
          </p>
        </CardContent>
      </Card>
    </div>
  );

  switch (stepType) {
    case 'send_whatsapp':
      return renderWhatsAppConfig();
    case 'wait':
      return renderWaitConfig();
    case 'update_lead_status':
      return renderUpdateLeadStatusConfig();
    case 'update_case_status':
      return renderUpdateCaseStatusConfig();
    case 'create_case':
      return renderCreateCaseConfig();
    default:
      return <div>סוג שלב לא נתמך</div>;
  }
};
