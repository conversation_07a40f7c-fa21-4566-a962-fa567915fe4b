import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Eye, Calendar, User, FileText, Loader2, Trash2 } from "lucide-react";
import { useCases } from "@/hooks/useCases";
import { CaseModal } from "@/components/case/CaseModal";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { useUserManagement } from "@/hooks/useUserManagement";

const CaseIntakePage = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { cases, caseTypes, isLoading, deleteCase } = useCases();
  const { users } = useUserManagement();

  const intakeCases = useMemo(() => 
    cases.filter(case_ => case_.status === "בקליטה"), 
    [cases]
  );

  const CasesTable = ({ cases }: { cases: typeof intakeCases }) => (
    <div className="card-professional rounded-lg overflow-hidden">
      <table className="professional-table">
        <thead>
          <tr>
            <th className="text-center">פעולות</th>
            <th className="text-center">ערך/זמן</th>
            <th className="text-center">דדליין</th>
            <th className="text-center">סוג התיק</th>
            <th className="text-center">משתמש מוקצה</th>
            <th className="text-center">לקוח</th>
            <th className="text-center">שם התיק</th>
          </tr>
        </thead>
        <tbody>
          {cases.map((case_) => (
            <tr
              key={case_.id}
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => navigate(`/case/${case_.id}`)}
            >
              <td className="text-center">
                <div className="flex items-center justify-center gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-8 w-8 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/case/${case_.id}`);
                    }}
                    title="צפה בתיק"
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (confirm('האם אתה בטוח שברצונך למחוק את התיק?')) {
                        deleteCase(case_.id);
                      }
                    }}
                    title="מחק תיק"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </td>
              <td className="text-sm text-center">
                <div className="space-y-1">
                  {case_.value && (
                    <div className="case-value">
                      ₪{case_.value.toLocaleString()}
                    </div>
                  )}
                  {case_.total_time_logged > 0 && (
                    <div className="hours-logged">
                      {Math.floor(case_.total_time_logged / 60)}:{(case_.total_time_logged % 60).toString().padStart(2, '0')} שעות
                    </div>
                  )}
                  {!case_.value && !case_.total_time_logged && (
                    <div className="text-muted-foreground">-</div>
                  )}
                </div>
              </td>
              <td className="text-sm text-center">
                <div className="flex items-center justify-center gap-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  {case_.deadline ? new Date(case_.deadline).toLocaleDateString('he-IL') : '-'}
                </div>
              </td>
              <td className="text-center">
                <span className="text-sm font-medium">{case_.case_type?.name || '-'}</span>
              </td>
              <td className="text-sm text-center">
                <div className="flex items-center justify-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  {case_.assigned_user_id
                    ? users.find(u => u.id === case_.assigned_user_id)?.full_name ||
                      users.find(u => u.id === case_.assigned_user_id)?.email ||
                      "משתמש לא נמצא"
                    : "לא מוקצה"
                  }
                </div>
              </td>
              <td className="text-center">
                <div className="flex items-center justify-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  {case_.lead?.full_name || '-'}
                </div>
              </td>
              <td className="font-medium text-center">
                <div className="flex items-center justify-center gap-2">
                  <FileText className="w-4 h-4 text-muted-foreground" />
                  {case_.title}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span>טוען תיקים...</span>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">תיקים בקליטה</h1>
            <p className="text-muted-foreground">תיקים חדשים הממתינים לעיבוד</p>
          </div>
          <Button 
            className="btn-professional flex items-center gap-2"
            onClick={() => setIsModalOpen(true)}
          >
            <Plus className="w-4 h-4" />
            צור תיק חדש
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-3 h-3 bg-warning rounded-full"></div>
              תיקים בקליטה ({intakeCases.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {intakeCases.length > 0 ? (
              <CasesTable cases={intakeCases} />
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                אין תיקים בקליטה כרגע
              </div>
            )}
          </CardContent>
        </Card>

        <CaseModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          caseTypes={caseTypes}
          onSuccess={() => setIsModalOpen(false)}
        />
      </div>
    </ErrorBoundary>
  );
};

export default CaseIntakePage;