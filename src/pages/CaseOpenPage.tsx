import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Eye, Calendar, User, FileText, Loader2, Trash2 } from "lucide-react";
import { useCases } from "@/hooks/useCases";
import { useUserManagement } from "@/hooks/useUserManagement";

const CaseOpenPage = () => {
  const navigate = useNavigate();
  const { cases, isLoading, deleteCase } = useCases();
  const { users } = useUserManagement();

  const openCases = cases.filter(case_ => case_.status === "פתוח");

  const CasesTable = ({ cases }: { cases: typeof openCases }) => (
    <div className="card-professional rounded-lg overflow-hidden">
      <table className="professional-table">
        <thead>
          <tr>
            <th className="text-center">פעולות</th>
            <th className="text-center">ערך/זמן</th>
            <th className="text-center">דדליין</th>
            <th className="text-center">סוג התיק</th>
            <th className="text-center">משתמש מוקצה</th>
            <th className="text-center">לקוח</th>
            <th className="text-center">שם התיק</th>
          </tr>
        </thead>
        <tbody>
          {cases.map((case_) => (
            <tr
              key={case_.id}
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => navigate(`/case/${case_.id}`)}
            >
              <td className="text-center">
                <div className="flex items-center justify-center gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-8 w-8 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/case/${case_.id}`);
                    }}
                    title="צפה בתיק"
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (confirm('האם אתה בטוח שברצונך למחוק את התיק?')) {
                        deleteCase(case_.id);
                      }
                    }}
                    title="מחק תיק"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </td>
              <td className="text-sm text-center">
                <div className="space-y-1">
                  {case_.value && (
                    <div className="case-value">
                      ₪{case_.value.toLocaleString()}
                    </div>
                  )}
                  {case_.total_time_logged > 0 && (
                    <div className="hours-logged">
                      {Math.floor(case_.total_time_logged / 60)}:{(case_.total_time_logged % 60).toString().padStart(2, '0')} שעות
                    </div>
                  )}
                  {!case_.value && !case_.total_time_logged && (
                    <div className="text-muted-foreground">-</div>
                  )}
                </div>
              </td>
              <td className="text-sm text-center">
                <div className="flex items-center justify-center gap-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  {case_.deadline ? new Date(case_.deadline).toLocaleDateString('he-IL') : "לא צוין"}
                </div>
              </td>
              <td className="text-center">
                <span className="text-sm font-medium">{case_.case_type?.name || "לא צוין"}</span>
              </td>
              <td className="text-sm text-center">
                <div className="flex items-center justify-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  {case_.assigned_user_id
                    ? users.find(u => u.id === case_.assigned_user_id)?.full_name ||
                      users.find(u => u.id === case_.assigned_user_id)?.email ||
                      "משתמש לא נמצא"
                    : "לא מוקצה"
                  }
                </div>
              </td>
              <td className="text-center">
                <div className="flex items-center justify-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  {case_.lead?.full_name || "לא צוין"}
                </div>
              </td>
              <td className="font-medium text-center">
                <div className="flex items-center justify-center gap-2">
                  <FileText className="w-4 h-4 text-muted-foreground" />
                  {case_.title}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">תיקים פתוחים</h1>
          <p className="text-muted-foreground">תיקים פעילים הדורשים טיפול שוטף</p>
        </div>
        <Button 
          className="btn-professional flex items-center gap-2"
          onClick={() => navigate('/cases/intake')}
        >
          <Plus className="w-4 h-4" />
          צור תיק חדש
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-3 h-3 bg-success rounded-full"></div>
            תיקים פתוחים ({openCases.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin" />
            </div>
          ) : openCases.length > 0 ? (
            <CasesTable cases={openCases} />
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="w-12 h-12 mx-auto mb-4" />
              <p>אין תיקים פתוחים</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CaseOpenPage;