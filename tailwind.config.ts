import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			fontFamily: {
				'assistant': ['Assistant', 'system-ui', 'sans-serif'],
				'inter': ['Inter', 'system-ui', 'sans-serif'],
			},
			colors: {
				border: 'hsl(var(--border, 210 16% 90%))',
				input: 'hsl(var(--input, 210 16% 95%))',
				ring: 'hsl(var(--ring, 210 100% 20%))',
				background: 'hsl(var(--background, 0 0% 100%))',
				foreground: 'hsl(var(--foreground, 222 84% 5%))',
				primary: {
					DEFAULT: 'hsl(var(--primary, 210 100% 20%))',
					foreground: 'hsl(var(--primary-foreground, 0 0% 100%))',
					light: 'hsl(var(--primary-light, 210 100% 30%))',
					dark: 'hsl(var(--primary-dark, 210 100% 15%))'
				},
				success: {
					DEFAULT: 'hsl(var(--success, 142 76% 36%))',
					foreground: 'hsl(var(--success-foreground, 0 0% 100%))'
				},
				warning: {
					DEFAULT: 'hsl(var(--warning, 25 95% 53%))',
					foreground: 'hsl(var(--warning-foreground, 0 0% 100%))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary, 210 16% 93%))',
					foreground: 'hsl(var(--secondary-foreground, 222 84% 5%))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive, 0 72% 51%))',
					foreground: 'hsl(var(--destructive-foreground, 0 0% 100%))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted, 210 16% 98%))',
					foreground: 'hsl(var(--muted-foreground, 222 84% 5%))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent, 180 100% 90%))',
					foreground: 'hsl(var(--accent-foreground, 180 100% 25%))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover, 0 0% 100%))',
					foreground: 'hsl(var(--popover-foreground, 222 84% 5%))'
				},
				card: {
					DEFAULT: 'hsl(var(--card, 0 0% 100%))',
					foreground: 'hsl(var(--card-foreground, 222 84% 5%))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background, 0 0% 100%))',
					foreground: 'hsl(var(--sidebar-foreground, 222 84% 5%))',
					primary: 'hsl(var(--sidebar-primary, 210 100% 20%))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground, 0 0% 100%))',
					accent: 'hsl(var(--sidebar-accent, 210 16% 93%))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground, 222 84% 5%))',
					border: 'hsl(var(--sidebar-border, 210 16% 90%))',
					ring: 'hsl(var(--sidebar-ring, 210 100% 20%))'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fadeIn 0.3s ease-in-out',
				'slide-in-right': 'slideInRight 0.3s ease-out',
				'bounce-in': 'bounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)'
			},
			backgroundImage: {
				'gradient-professional': 'var(--gradient-primary)',
				'gradient-hero': 'var(--gradient-hero)',
				'gradient-subtle': 'var(--gradient-subtle)'
			},
			boxShadow: {
				'professional-sm': 'var(--shadow-sm)',
				'professional': 'var(--shadow-md)',
				'professional-lg': 'var(--shadow-lg)',
				'professional-xl': 'var(--shadow-xl)'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
