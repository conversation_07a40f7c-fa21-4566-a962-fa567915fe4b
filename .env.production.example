# Legal Hebrew Nexus - Production Environment Variables
# This file shows what environment variables need to be set in Vercel
# DO NOT commit actual production values to git

# =============================================================================
# REQUIRED PRODUCTION ENVIRONMENT VARIABLES
# Set these in your Vercel dashboard under Settings > Environment Variables
# =============================================================================

# Supabase Configuration (SAME AS DEVELOPMENT)
VITE_SUPABASE_URL=https://jihaizhvpddinhdysscd.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppaGFpemh2cGRkaW5oZHlzc2NkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU4ODQwNjIsImV4cCI6MjA3MTQ2MDA2Mn0.EmbivKVP4hlM3WI5WpYOLrNvmmTACm9AnwrWRyMqc6I

# Application Configuration (UPDATE FOR PRODUCTION)
VITE_ENVIRONMENT=production
VITE_APP_URL=https://your-production-domain.vercel.app

# Feature Toggles (ENABLE FOR PRODUCTION)
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_SPEED_INSIGHTS=true
VITE_ENABLE_AUTO_REFRESH=true

# =============================================================================
# OPTIONAL PRODUCTION SERVICES
# Add these if you want additional monitoring/analytics
# =============================================================================

# Error Monitoring (Optional)
# VITE_SENTRY_DSN=https://<EMAIL>/project-id

# Google Analytics (Optional)
# VITE_GA_TRACKING_ID=G-XXXXXXXXXX

# =============================================================================
# HOW TO SET THESE IN VERCEL:
# =============================================================================
# 1. Go to your Vercel dashboard
# 2. Select your project
# 3. Go to Settings > Environment Variables
# 4. Add each variable above with its production value
# 5. Set Environment to "Production"
# 6. Redeploy your application

# =============================================================================
# SECURITY NOTES:
# =============================================================================
# - Never commit actual production values to git
# - Use Vercel's environment variable system
# - Keep this file as a reference only
# - The .env file should remain in .gitignore
