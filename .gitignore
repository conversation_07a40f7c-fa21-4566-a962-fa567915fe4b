# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables and secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Security and credentials
*.key
*.pem
*.p12
*.pfx
secrets/
credentials/

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.backup
*.bak
*.tmp

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Vercel
.vercel
*.vercel.app

# Documentation (local development only)
docs/

# Monitoring and logs
monitoring_logs
error_reports/
*.log.json

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Stores VSCode versions used for testing VSCode extensions
.vscode-test
