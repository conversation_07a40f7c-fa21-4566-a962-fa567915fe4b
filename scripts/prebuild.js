#!/usr/bin/env node

/**
 * Prebuild script for cache busting and version management
 * Generates version file and updates service worker with dynamic cache names
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Generate build timestamp and version
const buildTimestamp = Date.now();
const buildDate = new Date().toISOString();
const version = `v${buildTimestamp}`;

console.log('🔧 Running prebuild script...');
console.log(`📅 Build timestamp: ${buildTimestamp}`);
console.log(`🏷️  Version: ${version}`);

// Create version.json file for runtime version checking
const versionData = {
  version,
  buildTimestamp,
  buildDate,
  environment: process.env.NODE_ENV || 'development'
};

// Write version file to public directory
const versionPath = path.join(path.dirname(__dirname), 'public', 'version.json');
fs.writeFileSync(versionPath, JSON.stringify(versionData, null, 2));
console.log('✅ Generated version.json');

// Read and update service worker with dynamic cache names
const swPath = path.join(path.dirname(__dirname), 'public', 'sw.js');
const swTemplatePath = path.join(path.dirname(__dirname), 'public', 'sw.template.js');

// Check if template exists, if not create it from current sw.js
if (!fs.existsSync(swTemplatePath)) {
  console.log('📝 Creating service worker template...');
  const currentSw = fs.readFileSync(swPath, 'utf8');
  
  // Replace static cache names with placeholders
  const templateContent = currentSw
    .replace(/const CACHE_NAME = '[^']*';/, "const CACHE_NAME = 'legal-app-__VERSION__';")
    .replace(/const API_CACHE_NAME = '[^']*';/, "const API_CACHE_NAME = 'legal-app-api-__VERSION__';");
  
  fs.writeFileSync(swTemplatePath, templateContent);
  console.log('✅ Created service worker template');
}

// Read template and replace placeholders
const swTemplate = fs.readFileSync(swTemplatePath, 'utf8');
const updatedSw = swTemplate
  .replace(/__VERSION__/g, version)
  .replace(/__BUILD_TIMESTAMP__/g, buildTimestamp);

// Write updated service worker
fs.writeFileSync(swPath, updatedSw);
console.log('✅ Updated service worker with dynamic cache names');

// Create build info for postbuild script
const buildInfoPath = path.join(path.dirname(__dirname), '.build-info.json');
fs.writeFileSync(buildInfoPath, JSON.stringify({
  version,
  buildTimestamp,
  buildDate
}, null, 2));

console.log('🎉 Prebuild completed successfully!');
