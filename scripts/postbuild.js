#!/usr/bin/env node

/**
 * Postbuild script for final optimizations and cleanup
 * Copies version file to dist and performs final checks
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 Running postbuild script...');

// Read build info
const buildInfoPath = path.join(path.dirname(__dirname), '.build-info.json');
if (!fs.existsSync(buildInfoPath)) {
  console.error('❌ Build info not found. Make sure prebuild script ran successfully.');
  process.exit(1);
}

const buildInfo = JSON.parse(fs.readFileSync(buildInfoPath, 'utf8'));
console.log(`📦 Processing build ${buildInfo.version}`);

// Ensure version.json is in dist directory
const distVersionPath = path.join(path.dirname(__dirname), 'dist', 'version.json');
const publicVersionPath = path.join(path.dirname(__dirname), 'public', 'version.json');

if (fs.existsSync(publicVersionPath)) {
  // Copy version.json to dist if it exists
  if (fs.existsSync(path.dirname(distVersionPath))) {
    fs.copyFileSync(publicVersionPath, distVersionPath);
    console.log('✅ Copied version.json to dist directory');
  }
}

// Verify service worker was built correctly
const distSwPath = path.join(path.dirname(__dirname), 'dist', 'sw.js');
if (fs.existsSync(distSwPath)) {
  const swContent = fs.readFileSync(distSwPath, 'utf8');
  if (swContent.includes(buildInfo.version)) {
    console.log('✅ Service worker contains correct version');
  } else {
    console.warn('⚠️  Service worker may not have correct version');
  }
}

// Clean up temporary files
if (fs.existsSync(buildInfoPath)) {
  fs.unlinkSync(buildInfoPath);
  console.log('🧹 Cleaned up temporary build files');
}

console.log('🎉 Postbuild completed successfully!');
