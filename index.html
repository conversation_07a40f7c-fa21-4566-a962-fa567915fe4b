<!DOCTYPE html>
<html lang="he" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Assistant:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Preload critical resources -->
    <link rel="modulepreload" href="/src/main.tsx">
    <link rel="modulepreload" href="/src/App.tsx">
    <link rel="modulepreload" href="/src/index.css">
    <title>מערכת ניהול משרד עורכי דין</title>
    <meta name="description" content="מערכת ניהול משרד עורכי דין מתקדמת" />
    <meta name="author" content="Legal Practice Management" />

    <meta property="og:title" content="מערכת ניהול משרד עורכי דין" />
    <meta property="og:description" content="מערכת ניהול משרד עורכי דין מתקדמת" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/placeholder.svg" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="/placeholder.svg" />

    <!-- Critical CSS to prevent FOUC -->
    <style>
      /* Prevent flash of unstyled content */
      html, body {
        background-color: white !important;
        color: #0f172a !important;
        margin: 0;
        padding: 0;
        font-family: 'Assistant', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        direction: rtl;
      }

      #root {
        min-height: 100vh;
        background-color: white !important;
      }

      /* Hide content until CSS loads to prevent FOUC */
      .loading-protection {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }

      .loading-protection.loaded {
        opacity: 1;
      }
    </style>
  </head>

  <body style="background-color: white; margin: 0; padding: 0;">
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
