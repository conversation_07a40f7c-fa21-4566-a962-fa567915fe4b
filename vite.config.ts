import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 3000,
  },
  plugins: [
    react(),
    // Removed lovable-tagger for production deployment
    {
      name: 'build-scripts',
      buildStart: async () => {
        console.log('🔧 Running prebuild script...');
        try {
          await execAsync('node scripts/prebuild.js');
        } catch (error) {
          console.error('❌ Prebuild script failed:', error);
          throw error;
        }
      },
      writeBundle: async () => {
        console.log('🔧 Running postbuild script...');
        try {
          await execAsync('node scripts/postbuild.js');
        } catch (error) {
          console.error('❌ Postbuild script failed:', error);
          // Don't throw here to avoid breaking the build
        }
      }
    }
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    sourcemap: false, // Disable sourcemaps for production
    rollupOptions: {
      output: {
        // Add hash to filenames for cache busting
        entryFileNames: 'assets/[name].[hash].js',
        chunkFileNames: 'assets/[name].[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `assets/images/[name].[hash].[ext]`;
          }
          if (/css/i.test(ext)) {
            return `assets/css/[name].[hash].[ext]`;
          }
          return `assets/[name].[hash].[ext]`;
        },
        manualChunks: {
          vendor: ['react', 'react-dom'],
          supabase: ['@supabase/supabase-js'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          charts: ['recharts'],
          utils: ['date-fns', 'clsx', 'class-variance-authority']
        }
      }
    },
    // Optimize chunk size warnings
    chunkSizeWarningLimit: 1000,
    // Enable minification for better performance
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true
      }
    }
  },
  // Optimize dependencies
  optimizeDeps: {
    include: ['react', 'react-dom', '@supabase/supabase-js', '@tanstack/react-query']
  }
}));
